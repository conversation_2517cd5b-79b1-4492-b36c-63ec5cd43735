/**
 * Health Management Controller
 * Handles all health-related CRUD operations
 */

const mongodb = require('../../config/mongodb');
const logger = require('../../utils/logger');
const { ObjectId } = require('mongodb');

class HealthController {
  // Get all health records
  async getHealthRecords(req, res) {
    try {
      const db = await mongodb.getDb();
      const collection = db.collection('health_records');
      
      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const animalId = req.query.animalId;
      const recordType = req.query.recordType;
      
      // Build filter
      const filter = {};
      if (animalId) filter.animalId = animalId;
      if (recordType) filter.recordType = recordType;
      
      // Get records with pagination
      const records = await collection
        .find(filter)
        .sort({ date: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();
      
      const total = await collection.countDocuments(filter);
      
      res.json({
        success: true,
        data: records,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching health records:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch health records',
        error: error.message
      });
    }
  }

  // Get health record by ID
  async getHealthRecordById(req, res) {
    try {
      const db = await mongodb.getDb();
      const collection = db.collection('health_records');
      
      const record = await collection.findOne({ _id: new ObjectId(req.params.id) });
      
      if (!record) {
        return res.status(404).json({
          success: false,
          message: 'Health record not found'
        });
      }
      
      res.json({
        success: true,
        data: record
      });
    } catch (error) {
      logger.error('Error fetching health record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch health record',
        error: error.message
      });
    }
  }

  // Create new health record
  async createHealthRecord(req, res) {
    try {
      const db = await mongodb.getDb();
      const collection = db.collection('health_records');
      
      const {
        animalId,
        recordType,
        date,
        treatment,
        veterinarian,
        cost,
        notes,
        nextDueDate
      } = req.body;
      
      // Validate required fields
      if (!animalId || !recordType || !date) {
        return res.status(400).json({
          success: false,
          message: 'Animal ID, record type, and date are required'
        });
      }
      
      // Verify animal exists
      const animalsCollection = db.collection('animals');
      const animal = await animalsCollection.findOne({ tagNumber: animalId });
      if (!animal) {
        return res.status(404).json({
          success: false,
          message: 'Animal not found'
        });
      }
      
      const healthRecord = {
        animalId,
        recordType,
        date: new Date(date),
        treatment: treatment || '',
        veterinarian: veterinarian || '',
        cost: parseFloat(cost) || 0,
        notes: notes || '',
        nextDueDate: nextDueDate ? new Date(nextDueDate) : null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await collection.insertOne(healthRecord);
      
      res.status(201).json({
        success: true,
        message: 'Health record created successfully',
        data: { ...healthRecord, _id: result.insertedId }
      });
    } catch (error) {
      logger.error('Error creating health record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create health record',
        error: error.message
      });
    }
  }

  // Update health record
  async updateHealthRecord(req, res) {
    try {
      const db = await mongodb.getDb();
      const collection = db.collection('health_records');
      
      const updateData = { ...req.body };
      delete updateData._id;
      
      // Convert date strings to Date objects
      if (updateData.date) updateData.date = new Date(updateData.date);
      if (updateData.nextDueDate) updateData.nextDueDate = new Date(updateData.nextDueDate);
      if (updateData.cost) updateData.cost = parseFloat(updateData.cost);
      
      updateData.updatedAt = new Date();
      
      const result = await collection.updateOne(
        { _id: new ObjectId(req.params.id) },
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Health record not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Health record updated successfully'
      });
    } catch (error) {
      logger.error('Error updating health record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update health record',
        error: error.message
      });
    }
  }

  // Delete health record
  async deleteHealthRecord(req, res) {
    try {
      const db = await mongodb.getDb();
      const collection = db.collection('health_records');
      
      const result = await collection.deleteOne({ _id: new ObjectId(req.params.id) });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Health record not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Health record deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting health record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete health record',
        error: error.message
      });
    }
  }

  // Get health statistics
  async getHealthStatistics(req, res) {
    try {
      const db = await mongodb.getDb();
      const healthCollection = db.collection('health_records');
      const animalsCollection = db.collection('animals');
      
      // Get basic counts
      const totalRecords = await healthCollection.countDocuments();
      const totalAnimals = await animalsCollection.countDocuments();
      const healthyAnimals = await animalsCollection.countDocuments({ healthStatus: 'healthy' });
      const sickAnimals = await animalsCollection.countDocuments({ healthStatus: 'sick' });
      
      // Get records by type
      const recordsByType = await healthCollection.aggregate([
        { $group: { _id: '$recordType', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get recent records
      const recentRecords = await healthCollection
        .find({})
        .sort({ date: -1 })
        .limit(5)
        .toArray();
      
      // Get upcoming due dates
      const upcomingDue = await healthCollection
        .find({
          nextDueDate: {
            $gte: new Date(),
            $lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
          }
        })
        .sort({ nextDueDate: 1 })
        .limit(10)
        .toArray();
      
      res.json({
        success: true,
        data: {
          totalRecords,
          totalAnimals,
          healthyAnimals,
          sickAnimals,
          healthPercentage: totalAnimals > 0 ? Math.round((healthyAnimals / totalAnimals) * 100) : 0,
          recordsByType,
          recentRecords,
          upcomingDue
        }
      });
    } catch (error) {
      logger.error('Error fetching health statistics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch health statistics',
        error: error.message
      });
    }
  }

  // Get health overview
  async getHealth(req, res) {
    try {
      res.json({
        success: true,
        message: 'Health module is operational',
        data: {
          module: 'health',
          status: 'active',
          features: [
            'Health Records Management',
            'Vaccination Tracking',
            'Treatment History',
            'Veterinary Appointments',
            'Health Statistics'
          ]
        }
      });
    } catch (error) {
      logger.error('Error in health overview:', error);
      res.status(500).json({
        success: false,
        message: 'Health module error',
        error: error.message
      });
    }
  }
}

module.exports = new HealthController();
