#!/bin/bash

# AgriIntel Production Deployment Script
# Comprehensive deployment automation for Beta version

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="AgriIntel"
BACKEND_DIR="backend"
FRONTEND_DIR="frontend-web"
DEPLOY_USER="agriintel"
DEPLOY_HOST="your-server.com"
DEPLOY_PATH="/var/www/agriintel"
BACKUP_PATH="/var/backups/agriintel"
LOG_FILE="deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a $LOG_FILE
    exit 1
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Starting pre-deployment checks..."
    
    # Check if required files exist
    if [ ! -f "$BACKEND_DIR/package.json" ]; then
        error "Backend package.json not found"
    fi
    
    if [ ! -f "$FRONTEND_DIR/package.json" ]; then
        error "Frontend package.json not found"
    fi
    
    # Check if environment variables are set
    if [ -z "$MONGODB_URI" ]; then
        warning "MONGODB_URI not set - make sure to configure before deployment"
    fi
    
    if [ -z "$JWT_SECRET" ]; then
        warning "JWT_SECRET not set - make sure to configure before deployment"
    fi
    
    success "Pre-deployment checks completed"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    # Backend dependencies
    log "Installing backend dependencies..."
    cd $BACKEND_DIR
    npm ci --production
    cd ..
    
    # Frontend dependencies
    log "Installing frontend dependencies..."
    cd $FRONTEND_DIR
    npm ci
    cd ..
    
    success "Dependencies installed"
}

# Run tests
run_tests() {
    log "Running tests..."
    
    # Backend tests
    if [ -f "$BACKEND_DIR/package.json" ] && grep -q "test" "$BACKEND_DIR/package.json"; then
        log "Running backend tests..."
        cd $BACKEND_DIR
        npm test || warning "Backend tests failed"
        cd ..
    fi
    
    # Frontend tests
    if [ -f "$FRONTEND_DIR/package.json" ] && grep -q "test" "$FRONTEND_DIR/package.json"; then
        log "Running frontend tests..."
        cd $FRONTEND_DIR
        npm test -- --coverage --watchAll=false || warning "Frontend tests failed"
        cd ..
    fi
    
    success "Tests completed"
}

# Build frontend
build_frontend() {
    log "Building frontend for production..."
    
    cd $FRONTEND_DIR
    
    # Set production environment
    export NODE_ENV=production
    export REACT_APP_API_URL=https://api.agriintel.com
    export REACT_APP_VERSION=1.0.0-beta
    
    # Build the application
    npm run build
    
    # Check if build was successful
    if [ ! -d "build" ]; then
        error "Frontend build failed - build directory not found"
    fi
    
    cd ..
    success "Frontend build completed"
}

# Setup database
setup_database() {
    log "Setting up database..."
    
    cd $BACKEND_DIR
    
    # Create default users
    if [ -f "scripts/create-default-users.js" ]; then
        log "Creating default users..."
        node scripts/create-default-users.js || warning "Failed to create default users"
    fi
    
    # Populate sample data
    if [ -f "scripts/populate-realistic-data.js" ]; then
        log "Populating sample data..."
        node scripts/populate-realistic-data.js || warning "Failed to populate sample data"
    fi
    
    cd ..
    success "Database setup completed"
}

# Create systemd service
create_systemd_service() {
    log "Creating systemd service..."
    
    cat > agriintel.service << EOF
[Unit]
Description=AgriIntel Backend Service
After=network.target

[Service]
Type=simple
User=$DEPLOY_USER
WorkingDirectory=$DEPLOY_PATH/backend
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3002

# Logging
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=agriintel

[Install]
WantedBy=multi-user.target
EOF
    
    success "Systemd service file created"
}

# Create nginx configuration
create_nginx_config() {
    log "Creating nginx configuration..."
    
    cat > agriintel.nginx << EOF
server {
    listen 80;
    server_name agriintel.com www.agriintel.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name agriintel.com www.agriintel.com;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/agriintel.crt;
    ssl_certificate_key /etc/ssl/private/agriintel.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Frontend (React App)
    location / {
        root $DEPLOY_PATH/frontend-web/build;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3002/health;
        access_log off;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
EOF
    
    success "Nginx configuration created"
}

# Create backup script
create_backup_script() {
    log "Creating backup script..."
    
    cat > backup.sh << 'EOF'
#!/bin/bash

# AgriIntel Backup Script
BACKUP_DIR="/var/backups/agriintel"
DATE=$(date +%Y%m%d_%H%M%S)
MONGODB_URI="$MONGODB_URI"
DB_NAME="ampd_livestock"

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mongodump --uri="$MONGODB_URI" --db="$DB_NAME" --out="$BACKUP_DIR/db_$DATE"

# Application backup
tar -czf "$BACKUP_DIR/app_$DATE.tar.gz" -C /var/www agriintel

# Clean old backups (keep last 30 days)
find $BACKUP_DIR -name "db_*" -mtime +30 -exec rm -rf {} \;
find $BACKUP_DIR -name "app_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF
    
    chmod +x backup.sh
    success "Backup script created"
}

# Deploy to server (if remote deployment)
deploy_to_server() {
    if [ "$1" = "remote" ]; then
        log "Deploying to remote server..."
        
        # Create deployment package
        tar -czf agriintel-deployment.tar.gz \
            $BACKEND_DIR \
            $FRONTEND_DIR/build \
            agriintel.service \
            agriintel.nginx \
            backup.sh
        
        # Upload to server
        scp agriintel-deployment.tar.gz $DEPLOY_USER@$DEPLOY_HOST:/tmp/
        
        # Execute remote deployment
        ssh $DEPLOY_USER@$DEPLOY_HOST << 'ENDSSH'
            cd /tmp
            tar -xzf agriintel-deployment.tar.gz
            
            # Stop existing service
            sudo systemctl stop agriintel || true
            
            # Backup current deployment
            sudo cp -r /var/www/agriintel /var/backups/agriintel-$(date +%Y%m%d_%H%M%S) || true
            
            # Deploy new version
            sudo mkdir -p /var/www/agriintel
            sudo cp -r backend /var/www/agriintel/
            sudo cp -r build /var/www/agriintel/frontend-web/
            
            # Install systemd service
            sudo cp agriintel.service /etc/systemd/system/
            sudo systemctl daemon-reload
            sudo systemctl enable agriintel
            
            # Install nginx config
            sudo cp agriintel.nginx /etc/nginx/sites-available/agriintel
            sudo ln -sf /etc/nginx/sites-available/agriintel /etc/nginx/sites-enabled/
            sudo nginx -t && sudo systemctl reload nginx
            
            # Set permissions
            sudo chown -R agriintel:agriintel /var/www/agriintel
            
            # Start service
            sudo systemctl start agriintel
            
            # Install backup script
            sudo cp backup.sh /usr/local/bin/agriintel-backup
            sudo chmod +x /usr/local/bin/agriintel-backup
            
            # Setup cron for backups
            echo "0 2 * * * /usr/local/bin/agriintel-backup" | sudo crontab -u agriintel -
            
            echo "Deployment completed successfully!"
ENDSSH
        
        success "Remote deployment completed"
    else
        success "Local deployment files prepared"
    fi
}

# Main deployment process
main() {
    log "Starting AgriIntel deployment process..."
    
    pre_deployment_checks
    install_dependencies
    run_tests
    build_frontend
    setup_database
    create_systemd_service
    create_nginx_config
    create_backup_script
    
    # Check if remote deployment is requested
    if [ "$1" = "remote" ]; then
        deploy_to_server remote
    else
        log "Local deployment preparation completed."
        log "To deploy to remote server, run: ./deploy.sh remote"
        log "Make sure to configure environment variables on the server."
    fi
    
    success "🚀 AgriIntel deployment process completed successfully!"
    log "Next steps:"
    log "1. Configure SSL certificates"
    log "2. Set up monitoring and logging"
    log "3. Configure domain DNS"
    log "4. Test all functionality"
    log "5. Set up automated backups"
}

# Run main function
main "$@"
