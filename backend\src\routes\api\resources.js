/**
 * Resources API Routes
 *
 * This module provides API routes for resources management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');
const resourcesController = require('../../controllers/mongodb/resourcesController');

/**
 * @route GET /api/resources
 * @desc Get resources overview
 * @access Private
 */
router.get('/', resourcesController.getResourcesOverview);

/**
 * @route GET /api/resources/government
 * @desc Get government resources
 * @access Private
 */
router.get('/government', resourcesController.getGovernmentResources);

/**
 * @route GET /api/resources/auctions
 * @desc Get auction information
 * @access Private
 */
router.get('/auctions', resourcesController.getAuctionInformation);

/**
 * @route GET /api/resources/education
 * @desc Get educational resources
 * @access Private
 */
router.get('/education', resourcesController.getEducationalResources);

/**
 * @route GET /api/resources/guides
 * @desc Get all guides
 * @access Private
 */
router.get('/guides', async (req, res, next) => {
  try {
    // Return mock guides for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Cattle Breeding Guide', category: 'breeding', downloads: 120 },
        { id: '2', title: 'Animal Health Basics', category: 'health', downloads: 98 },
        { id: '3', title: 'Feed Management', category: 'feeding', downloads: 87 }
      ]
    });
  } catch (error) {
    logger.error('Error getting guides:', error);
    next(error);
  }
});

/**
 * @route GET /api/resources/training
 * @desc Get all training resources
 * @access Private
 */
router.get('/training', async (req, res, next) => {
  try {
    // Return mock training resources for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Animal Handling Training', category: 'handling', duration: '2 hours' },
        { id: '2', title: 'Vaccination Techniques', category: 'health', duration: '1.5 hours' },
        { id: '3', title: 'Feed Mixing Workshop', category: 'feeding', duration: '3 hours' }
      ]
    });
  } catch (error) {
    logger.error('Error getting training resources:', error);
    next(error);
  }
});

/**
 * @route GET /api/resources/support
 * @desc Get all support resources
 * @access Private
 */
router.get('/support', async (req, res, next) => {
  try {
    // Return mock support resources for now
    return res.json({
      success: true,
      data: [
        { id: '1', title: 'Technical Support', category: 'technical', contact: '<EMAIL>' },
        { id: '2', title: 'Veterinary Assistance', category: 'veterinary', contact: '<EMAIL>' },
        { id: '3', title: 'Financial Advice', category: 'financial', contact: '<EMAIL>' }
      ]
    });
  } catch (error) {
    logger.error('Error getting support resources:', error);
    next(error);
  }
});

module.exports = router;
