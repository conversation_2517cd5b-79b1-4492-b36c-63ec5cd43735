import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Agriculture,
  TrendingUp,
  Security,
  Analytics,
  Pets,
  LocalHospital,
  PlayArrow,
  ArrowForward,
  Language,
  Menu,
  Close
} from '@mui/icons-material';
import '../styles/premium-landing.css';

const PremiumLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features = [
    {
      icon: <Pets />,
      title: '🐄 Smart Livestock Tracking',
      description: 'Advanced RFID and IoT sensors for real-time monitoring of cattle, sheep, and goats. Track health, location, and behavior patterns across your South African farm operations.'
    },
    {
      icon: <LocalHospital />,
      title: '🩺 AI Health Management',
      description: 'AI-powered health diagnostics and predictive analytics to prevent diseases, optimize animal welfare, and reduce veterinary costs by up to 40%.'
    },
    {
      icon: <Analytics />,
      title: '📊 Advanced Analytics',
      description: 'Comprehensive insights and reporting tools specifically designed for South African livestock operations. Maximize productivity and profitability with data-driven decisions.'
    },
    {
      icon: <TrendingUp />,
      title: '💰 Financial Optimization',
      description: 'Smart financial planning and cost optimization tools tailored for South African farming. Track expenses, revenue, and ROI with currency-specific features.'
    },
    {
      icon: <Security />,
      title: '🔒 Enterprise Security',
      description: 'Bank-grade security with local South African data centers, cloud backup, and 99.9% uptime guarantee for your critical farm data.'
    },
    {
      icon: <Agriculture />,
      title: '🌱 Sustainable Farming',
      description: 'Environmental monitoring and sustainable farming practices designed for South African climate conditions. Reduce carbon footprint and improve efficiency.'
    }
  ];

  const stats = [
    { number: '15K+', label: 'South African Farmers' },
    { number: '750K+', label: 'Livestock Tracked' },
    { number: '99.9%', label: 'System Uptime' },
    { number: '40%', label: 'Cost Reduction' }
  ];

  const subscriptionTiers = [
    {
      name: 'Beta Access',
      price: 'Free',
      duration: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      features: [
        '🐄 Up to 50 animals',
        '📱 Mobile app access',
        '📊 Basic health monitoring',
        '📈 Simple reports',
        '📧 Email support',
        '🇿🇦 South African language support'
      ],
      buttonText: 'Start Free Trial',
      buttonAction: () => navigate('/beta'),
      popular: false,
      color: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
    },
    {
      name: 'Professional',
      price: 'R299',
      duration: 'per month',
      description: 'For growing commercial farms across SA',
      features: [
        '🐄 Up to 500 animals',
        '🤖 AI health analytics',
        '💰 Financial management (ZAR)',
        '🧬 Breeding optimization',
        '📞 Priority support',
        '📊 Custom reports',
        '🔗 API access',
        '🌍 Multi-location support'
      ],
      buttonText: 'Choose Professional',
      buttonAction: () => navigate('/register'),
      popular: true,
      color: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
    },
    {
      name: 'Enterprise',
      price: 'R599',
      duration: 'per month',
      description: 'For large commercial operations & cooperatives',
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights',
        '🏢 Multi-farm management',
        '📊 Advanced analytics',
        '👨‍💼 Dedicated account manager',
        '🔧 Custom integrations',
        '🏷️ White-label options',
        '🎓 Training & onboarding',
        '🏛️ Government compliance tools'
      ],
      buttonText: 'Contact Sales',
      buttonAction: () => navigate('/register'),
      popular: false,
      color: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
    }
  ];

  return (
    <div className="premium-landing">
      {/* Premium Navigation */}
      <nav className={`premium-nav ${isScrolled ? 'scrolled' : ''}`}>
        <div className="premium-nav-content">
          <motion.a 
            href="#" 
            className="premium-logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            AgriIntel
          </motion.a>
          
          <ul className="premium-nav-links">
            <li><a href="#features" className="premium-nav-link">Features</a></li>
            <li><a href="#pricing" className="premium-nav-link">Pricing</a></li>
            <li><a href="#about" className="premium-nav-link">About</a></li>
            <li><a href="#contact" className="premium-nav-link">Contact</a></li>
          </ul>

          <div className="premium-nav-actions">
            <motion.button
              className="premium-btn premium-btn-secondary"
              onClick={() => navigate('/beta')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Try Beta
            </motion.button>
            <motion.button
              className="premium-btn premium-btn-primary"
              onClick={() => navigate('/login')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Login
            </motion.button>
          </div>
        </div>
      </nav>

      {/* Premium Hero Section */}
      <section className="premium-hero" style={{
        background: `linear-gradient(135deg, rgba(46, 125, 50, 0.95) 0%, rgba(27, 94, 32, 0.9) 50%, rgba(56, 142, 60, 0.85) 100%),
                     url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%234CAF50" width="1200" height="800"/><g fill="%23ffffff" opacity="0.1"><circle cx="200" cy="200" r="100"/><circle cx="800" cy="300" r="80"/><circle cx="1000" cy="600" r="120"/><path d="M100,400 Q300,200 500,400 T900,400"/></g></svg>')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}>
        {/* Livestock-themed animated elements */}
        <div className="premium-particles">
          {Array.from({ length: 30 }).map((_, i) => (
            <motion.div
              key={i}
              className="premium-particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 15}s`,
                animationDuration: `${15 + Math.random() * 10}s`,
                background: i % 3 === 0 ? 'linear-gradient(45deg, #4CAF50, #81C784)' :
                           i % 3 === 1 ? 'linear-gradient(45deg, #2E7D32, #66BB6A)' :
                           'linear-gradient(45deg, #FFC107, #FFD54F)'
              }}
            />
          ))}
        </div>

        <motion.div
          className="premium-hero-content"
          style={{ y }}
        >
          <motion.h1
            className="premium-hero-title"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
            style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #E8F5E8 50%, #C8E6C9 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            🌾 AgriIntel
            <br />
            <span className="premium-hero-title-gradient" style={{
              background: 'linear-gradient(135deg, #FFC107 0%, #FF8F00 50%, #F57C00 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Smart Farming Revolution
            </span>
          </motion.h1>

          <motion.p
            className="premium-hero-subtitle"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.4 }}
            style={{ color: 'rgba(255, 255, 255, 0.95)', textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}
          >
            🐄 Transform your livestock management with AI-powered insights,
            real-time monitoring, and data-driven decisions that maximize productivity
            and profitability across South Africa's agricultural landscape.
          </motion.p>

          <motion.div 
            className="premium-hero-buttons"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.6 }}
          >
            <motion.button
              className="premium-btn premium-btn-primary"
              onClick={() => navigate('/beta')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <PlayArrow />
              Start Free Trial
            </motion.button>
            
            <motion.button
              className="premium-btn premium-btn-secondary"
              onClick={() => navigate('/demo')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowForward />
              Watch Demo
            </motion.button>
          </motion.div>

          {/* Hero Stats */}
          <motion.div 
            className="premium-hero-stats"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '60px',
              flexWrap: 'wrap',
              marginTop: '60px'
            }}
          >
            {stats.slice(0, 3).map((stat, index) => (
              <motion.div
                key={index}
                style={{
                  textAlign: 'center',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}
                whileHover={{ scale: 1.1 }}
              >
                <div className="premium-hero-stat-number">
                  {stat.number}
                </div>
                <div className="premium-hero-stat-label">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </section>

      {/* Premium Features Section */}
      <section className="premium-features" id="features">
        <div className="premium-container">
          <motion.div 
            className="premium-section-title"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2>Powerful Features for Modern Farmers</h2>
            <p>
              Everything you need to manage your livestock operation efficiently 
              and profitably with cutting-edge technology.
            </p>
          </motion.div>

          <div className="premium-features-grid">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="premium-feature-card"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ 
                  y: -12,
                  transition: { duration: 0.3 }
                }}
              >
                <motion.div 
                  className="premium-feature-icon"
                  whileHover={{ 
                    rotate: 360,
                    transition: { duration: 0.6 }
                  }}
                >
                  {feature.icon}
                </motion.div>
                <h3 className="premium-feature-title">{feature.title}</h3>
                <p className="premium-feature-description">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Premium Stats Section */}
      <section className="premium-stats">
        <div className="premium-container">
          <motion.div
            className="premium-stats-grid"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className="premium-stat-card"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.3 }
                }}
              >
                <motion.div
                  className="premium-stat-number"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 1, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  {stat.number}
                </motion.div>
                <div className="premium-stat-label">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Premium Pricing Section */}
      <section className="premium-pricing" id="pricing">
        <div className="premium-container">
          <motion.div
            className="premium-pricing-header"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="premium-pricing-title">
              Choose Your Plan
            </h2>
            <p className="premium-pricing-subtitle">
              Start with our free beta and upgrade as your farm grows
            </p>
          </motion.div>

          <div className="premium-pricing-grid">
            {subscriptionTiers.map((tier, index) => (
              <motion.div
                key={index}
                className={`premium-pricing-card ${tier.popular ? 'popular' : ''}`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -12,
                  transition: { duration: 0.3 }
                }}
              >
                {tier.popular && (
                  <div className="premium-pricing-badge">
                    Most Popular
                  </div>
                )}

                <div className={`premium-pricing-icon ${tier.name.toLowerCase().replace(' ', '')}`}>
                  {index === 0 ? '🚀' : index === 1 ? '⭐' : '👑'}
                </div>

                <h3 className="premium-pricing-name">
                  {tier.name}
                </h3>

                <div className="premium-pricing-price">
                  <span className={`premium-pricing-price-amount ${tier.name.toLowerCase().replace(' ', '')}`}>
                    {tier.price}
                  </span>
                  <span className="premium-pricing-duration">
                    {tier.duration}
                  </span>
                </div>

                <p className="premium-pricing-description">
                  {tier.description}
                </p>

                <ul className="premium-pricing-features">
                  {tier.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="premium-pricing-feature">
                      <span className={`premium-pricing-feature-icon ${tier.name.toLowerCase().replace(' ', '')}`}>
                        ✓
                      </span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <motion.button
                  className={`premium-btn premium-btn-primary premium-pricing-button ${tier.name.toLowerCase().replace(' ', '')}`}
                  onClick={tier.buttonAction}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tier.buttonText}
                </motion.button>
              </motion.div>
            ))}
          </div>
        </div>
      </section>



      {/* Premium CTA Section */}
      <section className="premium-cta">
        <div className="premium-container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="premium-cta-title">
              Ready to Transform Your Farm?
            </h2>
            <p className="premium-cta-description">
              Join thousands of farmers who have revolutionized their operations with AgriIntel.
              Start your free trial today and see the difference.
            </p>
            <motion.button
              className="premium-btn premium-btn-primary premium-cta-button"
              onClick={() => navigate('/beta')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <PlayArrow />
              Start Your Free Trial
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Premium Footer */}
      <footer className="premium-footer">
        <div className="premium-container">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="premium-footer-logo">
              AgriIntel
            </div>
            <p className="premium-footer-tagline">
              The Future of Smart Farming
            </p>
            <div className="premium-footer-links">
              <a href="#" className="premium-footer-link">Privacy Policy</a>
              <a href="#" className="premium-footer-link">Terms of Service</a>
              <a href="#" className="premium-footer-link">Support</a>
              <a href="#" className="premium-footer-link">Contact</a>
            </div>
            <p className="premium-footer-copyright">
              © 2024 AgriIntel. All rights reserved.
            </p>
          </motion.div>
        </div>
      </footer>
    </div>
  );
};

export default PremiumLandingPage;
