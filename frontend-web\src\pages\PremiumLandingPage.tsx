import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Agriculture,
  TrendingUp,
  Security,
  Analytics,
  Pets,
  LocalHospital,
  PlayArrow,
  ArrowForward,
  Language,
  Menu,
  Close,
  CheckCircle,
  Star,
  Phone,
  Email,
  LocationOn,
  Facebook,
  Twitter,
  LinkedIn,
  Instagram
} from '@mui/icons-material';
import '../styles/premium-landing.css';
import '../styles/FlareDesign.css';

const PremiumLandingPage: React.FC = () => {
  const navigate = useNavigate();
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('features');
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const features = [
    {
      icon: <Pets />,
      title: '🐄 Smart Livestock Tracking',
      description: 'Advanced RFID and IoT sensors for real-time monitoring of cattle, sheep, and goats. Track health, location, and behavior patterns across your South African farm operations.'
    },
    {
      icon: <LocalHospital />,
      title: '🩺 AI Health Management',
      description: 'AI-powered health diagnostics and predictive analytics to prevent diseases, optimize animal welfare, and reduce veterinary costs by up to 40%.'
    },
    {
      icon: <Analytics />,
      title: '📊 Advanced Analytics',
      description: 'Comprehensive insights and reporting tools specifically designed for South African livestock operations. Maximize productivity and profitability with data-driven decisions.'
    },
    {
      icon: <TrendingUp />,
      title: '💰 Financial Optimization',
      description: 'Smart financial planning and cost optimization tools tailored for South African farming. Track expenses, revenue, and ROI with currency-specific features.'
    },
    {
      icon: <Security />,
      title: '🔒 Enterprise Security',
      description: 'Bank-grade security with local South African data centers, cloud backup, and 99.9% uptime guarantee for your critical farm data.'
    },
    {
      icon: <Agriculture />,
      title: '🌱 Sustainable Farming',
      description: 'Environmental monitoring and sustainable farming practices designed for South African climate conditions. Reduce carbon footprint and improve efficiency.'
    }
  ];

  const stats = [
    { number: '15K+', label: 'South African Farmers', icon: '👨‍🌾' },
    { number: '750K+', label: 'Livestock Tracked', icon: '🐄' },
    { number: '99.9%', label: 'System Uptime', icon: '⚡' },
    { number: '40%', label: 'Cost Reduction', icon: '💰' }
  ];

  const testimonials = [
    {
      name: 'Johan van der Merwe',
      role: 'Commercial Farmer, Western Cape',
      image: '/images/testimonials/farmer1.jpg',
      quote: 'AgriIntel transformed our 500-head cattle operation. We reduced veterinary costs by 35% and increased productivity by 28%. The AI health monitoring is incredible!',
      rating: 5
    },
    {
      name: 'Nomsa Mthembu',
      role: 'Cooperative Manager, KwaZulu-Natal',
      image: '/images/testimonials/farmer2.jpg',
      quote: 'Managing 12 small farms was a nightmare before AgriIntel. Now we track 2000+ animals seamlessly. The mobile app works perfectly even in remote areas.',
      rating: 5
    },
    {
      name: 'Pieter Botha',
      role: 'Dairy Farmer, Free State',
      image: '/images/testimonials/farmer3.jpg',
      quote: 'The breeding optimization feature helped us improve our calving rate by 22%. ROI was achieved within 6 months. Best investment we ever made!',
      rating: 5
    }
  ];

  const galleryImages = [
    { src: '/images/gallery/cattle-farm-1.jpg', alt: 'Modern cattle farm with AgriIntel monitoring', category: 'farms' },
    { src: '/images/gallery/health-monitoring.jpg', alt: 'Veterinarian using AgriIntel health system', category: 'health' },
    { src: '/images/gallery/mobile-app.jpg', alt: 'Farmer using AgriIntel mobile app', category: 'technology' },
    { src: '/images/gallery/dairy-operation.jpg', alt: 'Automated dairy milking with AgriIntel', category: 'dairy' },
    { src: '/images/gallery/breeding-management.jpg', alt: 'Breeding records on AgriIntel dashboard', category: 'breeding' },
    { src: '/images/gallery/financial-dashboard.jpg', alt: 'Financial analytics and reporting', category: 'analytics' }
  ];

  const subscriptionTiers = [
    {
      name: 'Beta Access',
      price: 'Free',
      duration: '30 days trial',
      description: 'Perfect for small-scale South African farmers',
      features: [
        '🐄 Up to 50 animals',
        '📱 Mobile app access',
        '📊 Basic health monitoring',
        '📈 Simple reports',
        '📧 Email support',
        '🇿🇦 South African language support'
      ],
      buttonText: 'Start Free Trial',
      buttonAction: () => navigate('/beta'),
      popular: false,
      color: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)'
    },
    {
      name: 'Professional',
      price: 'R299',
      duration: 'per month',
      description: 'For growing commercial farms across SA',
      features: [
        '🐄 Up to 500 animals',
        '🤖 AI health analytics',
        '💰 Financial management (ZAR)',
        '🧬 Breeding optimization',
        '📞 Priority support',
        '📊 Custom reports',
        '🔗 API access',
        '🌍 Multi-location support'
      ],
      buttonText: 'Choose Professional',
      buttonAction: () => navigate('/register'),
      popular: true,
      color: 'linear-gradient(135deg, #2196F3 0%, #1565C0 100%)'
    },
    {
      name: 'Enterprise',
      price: 'R599',
      duration: 'per month',
      description: 'For large commercial operations & cooperatives',
      features: [
        '🐄 Unlimited animals',
        '🤖 AI-powered insights',
        '🏢 Multi-farm management',
        '📊 Advanced analytics',
        '👨‍💼 Dedicated account manager',
        '🔧 Custom integrations',
        '🏷️ White-label options',
        '🎓 Training & onboarding',
        '🏛️ Government compliance tools'
      ],
      buttonText: 'Contact Sales',
      buttonAction: () => navigate('/register'),
      popular: false,
      color: 'linear-gradient(135deg, #FF9800 0%, #F57C00 100%)'
    }
  ];

  return (
    <div className="premium-landing">
      {/* Premium Navigation */}
      <nav className={`premium-nav ${isScrolled ? 'scrolled' : ''}`}>
        <div className="premium-nav-content">
          <motion.a 
            href="#" 
            className="premium-logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            AgriIntel
          </motion.a>
          
          <ul className="premium-nav-links">
            <li><a href="#features" className="premium-nav-link">Features</a></li>
            <li><a href="#pricing" className="premium-nav-link">Pricing</a></li>
            <li><a href="#about" className="premium-nav-link">About</a></li>
            <li><a href="#contact" className="premium-nav-link">Contact</a></li>
          </ul>

          <div className="premium-nav-actions">
            <motion.button
              className="premium-btn premium-btn-secondary"
              onClick={() => navigate('/beta')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Try Beta
            </motion.button>
            <motion.button
              className="premium-btn premium-btn-primary"
              onClick={() => navigate('/login')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Login
            </motion.button>
          </div>
        </div>
      </nav>

      {/* Dynamic Hero Section - Flare Style */}
      <section className="flare-hero">
        {/* Background Video/Image */}
        <div className="flare-hero-bg">
          <div className="flare-bg-overlay"></div>
          <img
            src="https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
            alt="Modern Cattle Farm"
            className="flare-bg-image"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><rect fill="%234CAF50" width="1920" height="1080"/><text x="960" y="540" text-anchor="middle" fill="white" font-size="48">Modern Farm Technology</text></svg>';
            }}
          />
        </div>

        {/* Hero Content */}
        <div className="flare-hero-container">
          <div className="flare-hero-content">
            <motion.div
              className="flare-hero-badge"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="flare-badge-text">🌾 SOUTH AFRICA'S #1 LIVESTOCK PLATFORM</span>
            </motion.div>

            <motion.h1
              className="flare-hero-title"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <span className="flare-title-line1">REVOLUTIONIZE YOUR</span>
              <span className="flare-title-line2">LIVESTOCK FARM</span>
              <span className="flare-title-line3">WITH AI INTELLIGENCE</span>
            </motion.h1>

            <motion.p
              className="flare-hero-subtitle"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              Join 15,000+ South African farmers who've transformed their operations.
              Monitor 750,000+ animals, reduce costs by 40%, and maximize productivity.
            </motion.p>

            <motion.div
              className="flare-hero-actions"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <button
                type="button"
                className="flare-btn flare-btn-primary"
                onClick={() => navigate('/beta')}
              >
                <PlayArrow />
                START FREE TRIAL
              </button>
              <button
                type="button"
                className="flare-btn flare-btn-outline"
                onClick={() => navigate('/demo')}
              >
                <ArrowForward />
                WATCH DEMO
              </button>
            </motion.div>
          </div>

          {/* Floating Stats Cards */}
          <div className="flare-floating-stats">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                className={`flare-stat-card flare-stat-${index + 1}`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 1.5 + index * 0.2 }}
                whileHover={{ scale: 1.1, y: -10 }}
              >
                <div className="flare-stat-icon">{stat.icon}</div>
                <div className="flare-stat-number">{stat.number}</div>
                <div className="flare-stat-label">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          className="flare-scroll-indicator"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2 }}
        >
          <div className="flare-scroll-text">SCROLL TO EXPLORE</div>
          <div className="flare-scroll-arrow">↓</div>
        </motion.div>
      </section>

      {/* Davis Portfolio Style Features */}
      <section className="davis-features" id="features">
        <div className="davis-features-bg">
          <img
            src="https://images.unsplash.com/photo-1586771107445-d3ca888129ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Smart Farm Technology"
            className="davis-bg-image"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><rect fill="%232E7D32" width="1920" height="1080"/><text x="960" y="540" text-anchor="middle" fill="white" font-size="48">Smart Agriculture</text></svg>';
            }}
          />
          <div className="davis-bg-overlay"></div>
        </div>

        <div className="davis-container">
          <div className="davis-features-layout">
            {/* Left Side - Main Feature */}
            <motion.div
              className="davis-main-feature"
              initial={{ opacity: 0, x: -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 1 }}
              viewport={{ once: true }}
            >
              <div className="davis-feature-badge">
                <span>🚀 CUTTING-EDGE TECHNOLOGY</span>
              </div>

              <h2 className="davis-feature-title">
                POWERFUL FEATURES FOR
                <span className="davis-highlight"> MODERN FARMERS</span>
              </h2>

              <p className="davis-feature-description">
                Everything you need to manage your livestock operation efficiently
                and profitably with cutting-edge AI technology and real-time monitoring.
              </p>

              <div className="davis-feature-stats">
                <div className="davis-stat">
                  <span className="davis-stat-number">40%</span>
                  <span className="davis-stat-label">Cost Reduction</span>
                </div>
                <div className="davis-stat">
                  <span className="davis-stat-number">95%</span>
                  <span className="davis-stat-label">Health Accuracy</span>
                </div>
                <div className="davis-stat">
                  <span className="davis-stat-number">24/7</span>
                  <span className="davis-stat-label">Monitoring</span>
                </div>
              </div>

              <button
                type="button"
                className="davis-cta-btn"
                onClick={() => navigate('/beta')}
              >
                EXPLORE FEATURES
                <ArrowForward />
              </button>
            </motion.div>

            {/* Right Side - Feature Grid */}
            <div className="davis-features-grid">
              {features.slice(0, 6).map((feature, index) => (
                <motion.div
                  key={index}
                  className={`davis-feature-card davis-card-${index + 1}`}
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  whileInView={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{
                    scale: 1.05,
                    y: -10,
                    transition: { duration: 0.3 }
                  }}
                >
                  <div className="davis-card-icon">
                    {feature.icon}
                  </div>
                  <h3 className="davis-card-title">{feature.title}</h3>
                  <p className="davis-card-description">{feature.description}</p>
                  <div className="davis-card-arrow">→</div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="aqua-testimonials">
        <div className="premium-container">
          <motion.div
            className="aqua-section-header"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="aqua-section-title">
              Trusted by 15,000+ South African Farmers
            </h2>
            <p className="aqua-section-subtitle">
              See how AgriIntel is transforming livestock operations across the country
            </p>
          </motion.div>

          <div className="aqua-testimonials-grid">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="aqua-testimonial-card"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
              >
                <div className="aqua-testimonial-rating">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <Star key={i} className="aqua-star-icon" />
                  ))}
                </div>

                <p className="aqua-testimonial-quote">
                  "{testimonial.quote}"
                </p>

                <div className="aqua-testimonial-author">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="aqua-author-image"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="%234CAF50"/><text x="50" y="55" text-anchor="middle" fill="white" font-size="20">👨‍🌾</text></svg>';
                    }}
                  />
                  <div className="aqua-author-info">
                    <span className="aqua-author-name">{testimonial.name}</span>
                    <span className="aqua-author-role">{testimonial.role}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Dynamic Gallery Section */}
      <section className="dynamic-gallery">
        <div className="dynamic-gallery-bg">
          <img
            src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Agricultural Technology"
            className="dynamic-bg-image"
            onError={(e) => {
              (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><rect fill="%23388E3C" width="1920" height="1080"/><text x="960" y="540" text-anchor="middle" fill="white" font-size="48">Agricultural Innovation</text></svg>';
            }}
          />
          <div className="dynamic-bg-overlay"></div>
        </div>

        <div className="dynamic-container">
          <motion.div
            className="dynamic-gallery-header"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="dynamic-gallery-title">
              SEE AGRIINTEL IN ACTION
            </h2>
            <p className="dynamic-gallery-subtitle">
              Real farms, real results, real transformation across South Africa
            </p>
          </motion.div>

          {/* Dynamic Image Grid */}
          <div className="dynamic-gallery-grid">
            {/* Large Feature Image */}
            <motion.div
              className="dynamic-gallery-main"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <img
                src="https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
                alt="Modern Cattle Farm with AgriIntel Technology"
                className="dynamic-main-image"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600"><rect fill="%234CAF50" width="800" height="600"/><text x="400" y="300" text-anchor="middle" fill="white" font-size="32">Modern Farm</text></svg>';
                }}
              />
              <div className="dynamic-main-overlay">
                <h3>Modern Cattle Management</h3>
                <p>AI-powered monitoring across 500+ head of cattle</p>
              </div>
            </motion.div>

            {/* Side Images */}
            <div className="dynamic-gallery-side">
              {[
                {
                  src: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                  title: "Health Monitoring",
                  desc: "Real-time health analytics"
                },
                {
                  src: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80",
                  title: "Mobile Dashboard",
                  desc: "Manage from anywhere"
                },
                {
                  src: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
                  title: "Breeding Optimization",
                  desc: "Maximize genetic potential"
                },
                {
                  src: "https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80",
                  title: "Financial Analytics",
                  desc: "Track ROI and profitability"
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="dynamic-gallery-item"
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <img
                    src={item.src}
                    alt={item.title}
                    className="dynamic-item-image"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><rect fill="%234CAF50" width="400" height="300"/><text x="200" y="150" text-anchor="middle" fill="white" font-size="20">${item.title}</text></svg>`;
                    }}
                  />
                  <div className="dynamic-item-overlay">
                    <h4>{item.title}</h4>
                    <p>{item.desc}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Modern Pricing Section */}
      <section className="aqua-pricing" id="pricing">
        <div className="premium-container">
          <motion.div
            className="aqua-section-header"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="aqua-section-title">
              Choose Your Perfect Plan
            </h2>
            <p className="aqua-section-subtitle">
              Flexible pricing designed for South African farmers. Start free, scale as you grow.
            </p>
          </motion.div>

          {/* Pricing Toggle */}
          <motion.div
            className="aqua-pricing-toggle"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <span className="aqua-toggle-label">Monthly</span>
            <div className="aqua-toggle-switch">
              <input type="checkbox" id="pricing-toggle" aria-label="Toggle between monthly and annual pricing" />
              <label htmlFor="pricing-toggle"></label>
            </div>
            <span className="aqua-toggle-label">
              Annual <span className="aqua-save-badge">Save 20%</span>
            </span>
          </motion.div>

          <div className="aqua-pricing-grid">
            {subscriptionTiers.map((tier, index) => (
              <motion.div
                key={index}
                className={`aqua-pricing-card ${tier.popular ? 'popular' : ''}`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{
                  y: -12,
                  transition: { duration: 0.3 }
                }}
              >
                {tier.popular && (
                  <div className="aqua-pricing-badge">
                    <Star className="aqua-badge-icon" />
                    Most Popular
                  </div>
                )}

                <div className="aqua-pricing-header">
                  <div className={`aqua-pricing-icon ${tier.name.toLowerCase().replace(' ', '')}`}>
                    {index === 0 ? '🌱' : index === 1 ? '🚀' : '👑'}
                  </div>

                  <h3 className="aqua-pricing-name">{tier.name}</h3>

                  <div className="aqua-pricing-price">
                    <span className="aqua-price-currency">R</span>
                    <span className="aqua-price-amount">
                      {tier.price === 'Free' ? '0' : tier.price.replace('R', '')}
                    </span>
                    <span className="aqua-price-period">
                      {tier.price === 'Free' ? '/30 days' : '/month'}
                    </span>
                  </div>

                  <p className="aqua-pricing-description">{tier.description}</p>
                </div>

                <div className="aqua-pricing-features">
                  {tier.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="aqua-feature-item">
                      <CheckCircle className="aqua-feature-check" />
                      <span className="aqua-feature-text">{feature}</span>
                    </div>
                  ))}
                </div>

                <motion.button
                  className={`aqua-btn aqua-pricing-button ${tier.popular ? 'primary' : 'secondary'}`}
                  onClick={tier.buttonAction}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tier.buttonText}
                  <ArrowForward className="aqua-btn-icon" />
                </motion.button>

                {tier.popular && (
                  <div className="aqua-pricing-guarantee">
                    <CheckCircle className="aqua-guarantee-icon" />
                    <span>30-day money-back guarantee</span>
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Pricing FAQ */}
          <motion.div
            className="aqua-pricing-faq"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="aqua-faq-title">Frequently Asked Questions</h3>
            <div className="aqua-faq-grid">
              <div className="aqua-faq-item">
                <h4>Can I switch plans anytime?</h4>
                <p>Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
              </div>
              <div className="aqua-faq-item">
                <h4>Is there a setup fee?</h4>
                <p>No setup fees. We provide free onboarding and training for all paid plans.</p>
              </div>
              <div className="aqua-faq-item">
                <h4>What payment methods do you accept?</h4>
                <p>We accept all major credit cards, EFT, and direct debit in South African Rand.</p>
              </div>
              <div className="aqua-faq-item">
                <h4>Do you offer discounts for cooperatives?</h4>
                <p>Yes, we offer special pricing for agricultural cooperatives and large operations.</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>



      {/* Premium CTA Section */}
      <section className="premium-cta">
        <div className="premium-container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="premium-cta-title">
              Ready to Transform Your Farm?
            </h2>
            <p className="premium-cta-description">
              Join thousands of farmers who have revolutionized their operations with AgriIntel.
              Start your free trial today and see the difference.
            </p>
            <motion.button
              className="premium-btn premium-btn-primary premium-cta-button"
              onClick={() => navigate('/beta')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <PlayArrow />
              Start Your Free Trial
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Comprehensive Footer */}
      <footer className="aqua-footer">
        <div className="premium-container">
          {/* Footer Main Content */}
          <div className="aqua-footer-main">
            <motion.div
              className="aqua-footer-brand"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="aqua-footer-logo">
                <Agriculture className="aqua-logo-icon" />
                <span className="aqua-logo-text">AgriIntel</span>
              </div>
              <p className="aqua-footer-description">
                South Africa's leading livestock management platform. Empowering farmers
                with AI-driven insights and comprehensive farm management solutions.
              </p>
              <div className="aqua-footer-social">
                <a href="#" className="aqua-social-link" title="Follow us on Facebook">
                  <Facebook />
                </a>
                <a href="#" className="aqua-social-link" title="Follow us on Twitter">
                  <Twitter />
                </a>
                <a href="#" className="aqua-social-link" title="Connect with us on LinkedIn">
                  <LinkedIn />
                </a>
                <a href="#" className="aqua-social-link" title="Follow us on Instagram">
                  <Instagram />
                </a>
              </div>
            </motion.div>

            <motion.div
              className="aqua-footer-contact"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="aqua-footer-title">Get in Touch</h4>
              <div className="aqua-contact-info">
                <div className="aqua-contact-item">
                  <Phone className="aqua-contact-icon" />
                  <span>+27 11 123 4567</span>
                </div>
                <div className="aqua-contact-item">
                  <Email className="aqua-contact-icon" />
                  <span><EMAIL></span>
                </div>
                <div className="aqua-contact-item">
                  <LocationOn className="aqua-contact-icon" />
                  <span>Cape Town, South Africa</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Footer Bottom */}
          <motion.div
            className="aqua-footer-bottom"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <div className="aqua-footer-bottom-content">
              <div className="aqua-footer-copyright">
                © 2024 AgriIntel. All rights reserved. Made with ❤️ for South African farmers.
              </div>
              <div className="aqua-footer-legal">
                <a href="#privacy">Privacy Policy</a>
                <a href="#terms">Terms of Service</a>
                <a href="#cookies">Cookie Policy</a>
              </div>
            </div>
          </motion.div>
        </div>
      </footer>
    </div>
  );
};

export default PremiumLandingPage;
