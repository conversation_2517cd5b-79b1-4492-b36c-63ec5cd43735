/**
 * Authentication Service
 *
 * Provides centralized authentication functionality with proper password handling
 * and user management.
 * Note: Using mock data for now - database integration to be added later
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const logger = require('../utils/logger');
const mongodb = require('../config/mongodb');

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || 'agriintel-secret-key-2024';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Password hashing configuration
const SALT_ROUNDS = 12;

/**
 * Hash a password using bcrypt
 * @param {string} password - Plain text password
 * @returns {Promise<string>} - Hashed password
 */
const hashPassword = async (password) => {
  try {
    return await bcrypt.hash(password, SALT_ROUNDS);
  } catch (error) {
    logger.error('Error hashing password:', error);
    throw new Error('Password hashing failed');
  }
};

/**
 * Verify a password against its hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} - True if password matches
 */
const verifyPassword = async (password, hash) => {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    logger.error('Error verifying password:', error);
    return false;
  }
};

// NOTE: Mock users removed for security - all authentication now uses MongoDB
// Default users are created via database seeding scripts with proper password hashing

/**
 * Compare a password with a hashed password
 * @param {string} password - Plain text password
 * @param {string} hashedPassword - Hashed password
 * @returns {Promise<boolean>} - True if passwords match
 */
async function comparePassword(password, hashedPassword) {
  try {
    logger.debug(`Comparing password with hash: ${hashedPassword.substring(0, 10)}...`);

    // First try bcrypt
    try {
      const result = await bcrypt.compare(password, hashedPassword);
      logger.debug(`bcrypt comparison result: ${result}`);
      return result;
    } catch (bcryptError) {
      logger.warn('bcrypt comparison failed, trying SHA-256:', bcryptError.message);

      // Fall back to SHA-256 for legacy passwords
      const sha256Hash = crypto.createHash('sha256').update(password).digest('hex');
      const result = sha256Hash === hashedPassword;
      logger.debug(`SHA-256 comparison result: ${result}`);
      return result;
    }
  } catch (error) {
    logger.error('Error comparing passwords:', error);
    throw error; // Throw the error to be caught by the caller
  }
}

/**
 * Generate a JWT token
 * @param {Object} payload - Token payload
 * @returns {string} - JWT token
 */
function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
}

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} - Decoded token payload or null if invalid
 */
function verifyToken(token) {
  try {
    // Make sure the token is a string
    if (typeof token !== 'string') {
      logger.error('Invalid token type:', typeof token);
      return null;
    }

    // Check if token is in valid format
    if (!token.match(/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/)) {
      logger.error('Token format is invalid');
      return null;
    }

    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);

    // Check if decoded token has required fields
    if (!decoded.id || !decoded.username || !decoded.role) {
      logger.error('Token missing required fields');
      return null;
    }

    return decoded;
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      logger.warn('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      logger.warn(`JWT error: ${error.message}`);
    } else {
      logger.error('Error verifying token:', error);
    }
    return null;
  }
}

/**
 * Generate a password reset token
 * @returns {string} - Password reset token
 */
function generateResetToken() {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Authenticate a user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Promise<Object>} - Authentication result
 */
async function authenticateUser(username, password) {
  try {
    logger.info(`Authenticating user: ${username}`);

    // Connect to MongoDB if not already connected
    const { db } = await mongodb.connectDB();

    // Find user by username using MongoDB native driver
    const user = await db.collection('users').findOne({ username });

    if (!user) {
      logger.warn(`User not found: ${username}`);
      return { success: false, message: 'Invalid credentials' };
    }

    // Check if user is active
    if (user.status === 'inactive' || user.isActive === false) {
      logger.warn(`Inactive account: ${username}`);
      return { success: false, message: 'Account is inactive' };
    }

    // Check if user is suspended
    if (user.status === 'suspended') {
      logger.warn(`Suspended account: ${username}`);
      return { success: false, message: 'Account is suspended' };
    }

    // Verify password
    logger.info(`Verifying password for user: ${username}`);
    logger.debug(`Stored password hash: ${user.password}`);

    try {
      const isPasswordValid = await comparePassword(password, user.password);

      if (!isPasswordValid) {
        logger.warn(`Invalid password for user: ${username}`);
        return { success: false, message: 'Invalid credentials' };
      }

      logger.info(`Password verified successfully for user: ${username}`);
    } catch (error) {
      logger.error(`Error verifying password for user: ${username}:`, error);
      return { success: false, message: 'Authentication error', error };
    }

    // Generate JWT token
    const token = generateToken({
      id: user._id.toString(),
      username: user.username,
      role: user.role,
      firstName: user.firstName,
      lastName: user.lastName
    });

    // Update last login time using MongoDB native driver
    await db.collection('users').updateOne(
      { _id: user._id },
      { $set: { lastLogin: new Date(), updatedAt: new Date() } }
    );

    // Return user data and token
    const userData = {
      id: user._id.toString(),
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      permissions: user.permissions || [],
      status: user.status || 'active',
      lastLogin: new Date(),
      department: user.department,
      position: user.position,
      phoneNumber: user.phoneNumber,
      profileImage: user.profileImage
    };

    logger.info(`User authenticated successfully: ${username}`);
    return {
      success: true,
      user: userData,
      token
    };
  } catch (error) {
    logger.error('Authentication error:', error);
    return { success: false, message: 'Authentication failed', error };
  }
}

module.exports = {
  hashPassword,
  comparePassword,
  generateToken,
  verifyToken,
  generateResetToken,
  authenticateUser
};
