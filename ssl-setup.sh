#!/bin/bash

# AgriIntel SSL Certificate Setup Script
# Automated SSL certificate generation and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOMAIN="agriintel.com"
EMAIL="<EMAIL>"
WEBROOT="/var/www/agriintel/frontend-web/build"
SSL_DIR="/etc/ssl/agriintel"
NGINX_CONFIG="/etc/nginx/sites-available/agriintel"

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        error "This script must be run as root (use sudo)"
    fi
}

# Install Certbot
install_certbot() {
    log "Installing Certbot..."
    
    # Update package list
    apt-get update
    
    # Install snapd if not present
    if ! command -v snap &> /dev/null; then
        apt-get install -y snapd
    fi
    
    # Install certbot via snap
    snap install core; snap refresh core
    snap install --classic certbot
    
    # Create symlink
    ln -sf /snap/bin/certbot /usr/bin/certbot
    
    success "Certbot installed successfully"
}

# Generate SSL certificate
generate_certificate() {
    log "Generating SSL certificate for $DOMAIN..."
    
    # Stop nginx temporarily
    systemctl stop nginx || true
    
    # Generate certificate using standalone mode
    certbot certonly \
        --standalone \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        --domains $DOMAIN,www.$DOMAIN
    
    if [ $? -eq 0 ]; then
        success "SSL certificate generated successfully"
    else
        error "Failed to generate SSL certificate"
    fi
    
    # Start nginx
    systemctl start nginx
}

# Create custom SSL directory and copy certificates
setup_ssl_directory() {
    log "Setting up SSL directory..."
    
    # Create SSL directory
    mkdir -p $SSL_DIR
    
    # Copy certificates
    cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $SSL_DIR/agriintel.crt
    cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $SSL_DIR/agriintel.key
    
    # Set proper permissions
    chmod 644 $SSL_DIR/agriintel.crt
    chmod 600 $SSL_DIR/agriintel.key
    chown root:root $SSL_DIR/*
    
    success "SSL directory configured"
}

# Generate DH parameters for enhanced security
generate_dhparam() {
    log "Generating DH parameters (this may take a while)..."
    
    if [ ! -f $SSL_DIR/dhparam.pem ]; then
        openssl dhparam -out $SSL_DIR/dhparam.pem 2048
        chmod 644 $SSL_DIR/dhparam.pem
        success "DH parameters generated"
    else
        warning "DH parameters already exist"
    fi
}

# Create enhanced nginx SSL configuration
create_nginx_ssl_config() {
    log "Creating enhanced nginx SSL configuration..."
    
    cat > $NGINX_CONFIG << EOF
# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN www.$DOMAIN;
    
    # ACME challenge for Let's Encrypt
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
    }
    
    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL Configuration
    ssl_certificate $SSL_DIR/agriintel.crt;
    ssl_certificate_key $SSL_DIR/agriintel.key;
    ssl_dhparam $SSL_DIR/dhparam.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate $SSL_DIR/agriintel.crt;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.$DOMAIN;" always;
    
    # Root directory
    root $WEBROOT;
    index index.html index.htm;
    
    # Frontend (React App)
    location / {
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary "Accept-Encoding";
        }
        
        # Security for sensitive files
        location ~* \.(env|log|conf)$ {
            deny all;
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Security headers for API
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options DENY always;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://localhost:3002/health;
        access_log off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
        allow all;
    }
    
    # Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;
    
    # Logging
    access_log /var/log/nginx/agriintel_access.log;
    error_log /var/log/nginx/agriintel_error.log;
}
EOF
    
    success "Enhanced nginx SSL configuration created"
}

# Setup automatic certificate renewal
setup_auto_renewal() {
    log "Setting up automatic certificate renewal..."
    
    # Create renewal script
    cat > /usr/local/bin/agriintel-ssl-renew << 'EOF'
#!/bin/bash

# AgriIntel SSL Certificate Renewal Script
LOG_FILE="/var/log/agriintel-ssl-renew.log"

echo "$(date): Starting SSL certificate renewal" >> $LOG_FILE

# Renew certificates
certbot renew --quiet --no-self-upgrade >> $LOG_FILE 2>&1

if [ $? -eq 0 ]; then
    echo "$(date): Certificate renewal successful" >> $LOG_FILE
    
    # Copy renewed certificates
    cp /etc/letsencrypt/live/agriintel.com/fullchain.pem /etc/ssl/agriintel/agriintel.crt
    cp /etc/letsencrypt/live/agriintel.com/privkey.pem /etc/ssl/agriintel/agriintel.key
    
    # Reload nginx
    systemctl reload nginx
    echo "$(date): Nginx reloaded with new certificates" >> $LOG_FILE
else
    echo "$(date): Certificate renewal failed" >> $LOG_FILE
fi
EOF
    
    chmod +x /usr/local/bin/agriintel-ssl-renew
    
    # Add to crontab (run twice daily)
    (crontab -l 2>/dev/null; echo "0 2,14 * * * /usr/local/bin/agriintel-ssl-renew") | crontab -
    
    success "Automatic renewal configured"
}

# Test SSL configuration
test_ssl_config() {
    log "Testing SSL configuration..."
    
    # Test nginx configuration
    nginx -t
    
    if [ $? -eq 0 ]; then
        success "Nginx configuration is valid"
        
        # Reload nginx
        systemctl reload nginx
        success "Nginx reloaded successfully"
    else
        error "Nginx configuration test failed"
    fi
}

# Main function
main() {
    log "Starting AgriIntel SSL setup..."
    
    check_root
    install_certbot
    generate_certificate
    setup_ssl_directory
    generate_dhparam
    create_nginx_ssl_config
    setup_auto_renewal
    test_ssl_config
    
    success "🔒 SSL setup completed successfully!"
    log "Your site is now secured with SSL/TLS encryption"
    log "Certificate will auto-renew every 60 days"
    log "Test your SSL configuration at: https://www.ssllabs.com/ssltest/"
}

# Run main function
main "$@"
