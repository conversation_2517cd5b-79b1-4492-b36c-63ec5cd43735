export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  subscriptionTier: 'Beta Access' | 'Professional' | 'Enterprise';
  lastLogin?: Date;
  permissions?: string[]; // Array of permission strings
  createdAt: Date;
  updatedAt: Date;
  phoneNumber?: string;
  position?: string;
  department?: string;
  profileImage?: string;
}

export interface UserPermission {
  module: string;
  action: string;
  allowed: boolean;
}

export interface UserFormData {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  confirmPassword?: string;
  role: 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  subscriptionTier?: 'Beta Access' | 'Professional' | 'Enterprise';
  permissions?: string[];
  phoneNumber?: string;
  position?: string;
  department?: string;
  profileImage?: string;
}
