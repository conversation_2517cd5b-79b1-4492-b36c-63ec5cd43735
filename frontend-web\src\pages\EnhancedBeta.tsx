import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  But<PERSON>,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Chip,
  Alert,
  LinearProgress,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  useTheme,
  alpha
} from '@mui/material';
import {
  Agriculture,
  Dashboard,
  Pets,
  LocalHospital,
  TrendingUp,
  AccountBalance,
  Inventory,
  Assessment,
  Settings,
  Lock,
  Star,
  Upgrade,
  Timer,
  Person,
  ExitToApp,
  Language,
  Palette,
  NotificationsActive,
  Security,
  Visibility
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import LanguageSelector from '../components/LanguageSelector';
import { useTranslation } from '../hooks/useTranslation';
import ModernConnectedCard from '../components/common/ModernConnectedCard';
import ImageShowcase from '../components/showcase/ImageShowcase';
import { hasModuleAccess, getUpgradeMessage, moduleAccessConfig } from '../utils/betaAccessControl';
import { useAuth } from '../contexts/AuthContext';

// Modern unified design system (same as landing page)
const lifeNatureDesign = {
  colors: {
    // Primary Green Life Palette
    primary: '#2E7D32', // Deep Forest Green
    primaryLight: '#4CAF50', // Life Green
    primaryDark: '#1B5E20', // Dark Forest

    // Secondary Nature Palette
    secondary: '#388E3C', // Nature Green
    secondaryLight: '#66BB6A', // Fresh Green
    secondaryDark: '#2E7D32', // Deep Nature

    // Accent Colors
    accent: '#81C784', // Soft Life Green
    accentBright: '#A5D6A7', // Bright Nature
    accentGold: '#FFC107', // Golden Harvest

    // Surface & Background
    surface: 'rgba(76, 175, 80, 0.15)', // Translucent Green
    surfaceLight: 'rgba(129, 199, 132, 0.1)', // Light Green Surface
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)',

    // Text Colors
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)',
    textAccent: '#E8F5E8',

    // Gradients
    gradient: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    accentGradient: 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)',
    cardGradient: 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.1) 100%)'
  },
  shapes: {
    borderRadius: {
      small: '12px',
      medium: '20px',
      large: '32px',
      xl: '48px'
    },
    clipPath: {
      hexagon: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
      diamond: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
      leaf: 'polygon(50% 0%, 80% 10%, 100% 35%, 85% 70%, 50% 100%, 15% 70%, 0% 35%, 20% 10%)',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%',
      circle: '50%',
      pentagon: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)'
    }
  },
  gradients: {
    primary: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
    secondary: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 100%)',
    accent: 'linear-gradient(135deg, #81C784 0%, #A5D6A7 100%)',
    life: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    nature: 'linear-gradient(45deg, #2E7D32 0%, #388E3C 25%, #4CAF50 50%, #66BB6A 75%, #81C784 100%)',
    harvest: 'linear-gradient(135deg, #4CAF50 0%, #FFC107 50%, #FF8F00 100%)',
    pageBackground: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)'
  }
};

// Modern motion graphics background (same as landing page)
const MotionGraphicsBackground: React.FC = () => {
  const shapes = Array.from({ length: 12 }, (_, i) => ({
    id: i,
    shape: Object.values(lifeNatureDesign.shapes.clipPath)[i % 5],
    size: Math.random() * 80 + 40,
    x: Math.random() * 100,
    y: Math.random() * 100,
    duration: Math.random() * 25 + 20,
    delay: Math.random() * 5
  }));

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        overflow: 'hidden',
        pointerEvents: 'none',
        zIndex: 0
      }}
    >
      {/* Animated gradient background */}
      <motion.div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: lifeNatureDesign.colors.gradient,
        }}
        animate={{
          background: [
            lifeNatureDesign.colors.gradient,
            'linear-gradient(135deg, #1A1F3A 0%, #0A0E27 50%, #2D1B69 100%)',
            lifeNatureDesign.colors.gradient
          ]
        }}
        transition={{ duration: 12, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Floating geometric shapes */}
      {shapes.map((shape) => (
        <motion.div
          key={shape.id}
          style={{
            position: 'absolute',
            width: shape.size,
            height: shape.size,
            background: `linear-gradient(45deg, ${alpha(lifeNatureDesign.colors.accent, 0.08)}, ${alpha(lifeNatureDesign.colors.accentBright, 0.08)})`,
            clipPath: shape.shape,
            left: `${shape.x}%`,
            top: `${shape.y}%`,
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.5, 0.2],
            x: [0, Math.random() * 50 - 25, 0],
            y: [0, Math.random() * 50 - 25, 0],
          }}
          transition={{
            duration: shape.duration,
            repeat: Infinity,
            ease: "easeInOut",
            delay: shape.delay,
          }}
        />
      ))}
    </Box>
  );
};

// Modern shaped card component (same as landing page)
interface ModernCardProps {
  children: React.ReactNode;
  shape?: 'hexagon' | 'diamond' | 'arrow' | 'pentagon' | 'parallelogram' | 'rounded';
  variant?: 'glass' | 'solid' | 'gradient' | 'neon';
  size?: 'small' | 'medium' | 'large';
  animate?: boolean;
  sx?: any;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  shape = 'rounded',
  variant = 'glass',
  size = 'medium',
  animate = true,
  sx = {}
}) => {
  const getCardStyles = () => {
    const baseStyles = {
      position: 'relative',
      overflow: 'hidden',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    };

    const sizeStyles = {
      small: { minHeight: '180px' },
      medium: { minHeight: '250px' },
      large: { minHeight: '320px' }
    };

    const shapeStyles = {
      hexagon: {
        clipPath: lifeNatureDesign.shapes.clipPath.hexagon,
        borderRadius: 0
      },
      diamond: {
        clipPath: lifeNatureDesign.shapes.clipPath.diamond,
        borderRadius: 0,
        transform: 'rotate(0deg)'
      },
      arrow: {
        clipPath: lifeNatureDesign.shapes.clipPath.leaf,
        borderRadius: 0
      },
      pentagon: {
        clipPath: lifeNatureDesign.shapes.clipPath.pentagon,
        borderRadius: 0
      },
      parallelogram: {
        clipPath: lifeNatureDesign.shapes.clipPath.organic,
        borderRadius: 0
      },
      rounded: {
        borderRadius: lifeNatureDesign.shapes.borderRadius.large
      }
    };

    const variantStyles = {
      glass: {
        background: lifeNatureDesign.colors.surface,
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.2)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.3)}`
      },
      solid: {
        background: lifeNatureDesign.colors.secondary,
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.4)}`
      },
      gradient: {
        background: lifeNatureDesign.colors.cardGradient,
        border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.accent, 0.2)}`
      },
      neon: {
        background: lifeNatureDesign.colors.surface,
        border: `2px solid ${lifeNatureDesign.colors.accent}`,
        boxShadow: `0 0 20px ${alpha(lifeNatureDesign.colors.accent, 0.5)}, inset 0 0 20px ${alpha(lifeNatureDesign.colors.accent, 0.1)}`
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...shapeStyles[shape],
      ...variantStyles[variant],
      ...sx
    };
  };

  const cardContent = (
    <Box sx={getCardStyles()}>
      {/* Animated background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at 30% 30%, ${alpha(lifeNatureDesign.colors.accent, 0.08)} 0%, transparent 50%)`,
          zIndex: 0
        }}
      />

      {/* Content */}
      <Box sx={{ position: 'relative', zIndex: 1, height: '100%', p: 3 }}>
        {children}
      </Box>
    </Box>
  );

  if (!animate) return cardContent;

  return (
    <motion.div
      whileHover={{
        scale: 1.02,
        y: -8,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      {cardContent}
    </motion.div>
  );
};

interface BetaModule {
  id: string;
  name: string;
  icon: React.ReactElement;
  description: string;
  isLocked: boolean;
  isPremium: boolean;
  comingSoon?: boolean;
  color: string;
  route?: string;
  backgroundImage?: string;
}

const EnhancedBeta: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useTranslation();
  const { user: authUser } = useAuth();

  const [user, setUser] = useState({
    name: authUser?.firstName || 'Beta User',
    email: authUser?.email || '<EMAIL>',
    trialDaysLeft: 27,
    animalsCount: 12,
    maxAnimals: 50
  });
  
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [profileAnchor, setProfileAnchor] = useState<null | HTMLElement>(null);
  const [selectedModule, setSelectedModule] = useState<BetaModule | null>(null);
  const [showImageShowcase, setShowImageShowcase] = useState(false);

  // Fix for white blur overlay issue
  React.useEffect(() => {
    // Remove any persistent overlays
    const removeOverlays = () => {
      const overlays = document.querySelectorAll('.MuiBackdrop-root:not(.MuiBackdrop-open)');
      overlays.forEach(overlay => {
        (overlay as HTMLElement).style.display = 'none';
        (overlay as HTMLElement).style.pointerEvents = 'none';
      });

      // Ensure body doesn't have blur
      document.body.style.filter = 'none';
      document.body.style.backdropFilter = 'none';

      // Remove any white overlays
      const whiteOverlays = document.querySelectorAll('[style*="rgba(255, 255, 255"]');
      whiteOverlays.forEach(overlay => {
        if ((overlay as HTMLElement).style.zIndex === '1300' ||
            (overlay as HTMLElement).style.position === 'fixed') {
          (overlay as HTMLElement).style.display = 'none';
        }
      });
    };

    removeOverlays();
    const interval = setInterval(removeOverlays, 1000);

    return () => clearInterval(interval);
  }, []);

  // Enhanced livestock statistics
  const livestockStats = {
    totalAnimals: 247,
    healthyAnimals: 231,
    pregnantAnimals: 18,
    newBorns: 12,
    avgWeight: 485,
    milkProduction: 1250,
    feedConsumption: 2840,
    vaccinations: 45,
    breeds: ['Holstein', 'Angus', 'Hereford', 'Simmental'],
    recentActivities: [
      { type: 'birth', animal: 'Cow #247', time: '2 hours ago', icon: '🐄' },
      { type: 'vaccination', animal: 'Bull #023', time: '4 hours ago', icon: '💉' },
      { type: 'health_check', animal: 'Heifer #156', time: '6 hours ago', icon: '🩺' },
      { type: 'feeding', animal: 'Herd A', time: '8 hours ago', icon: '🌾' }
    ],
    alerts: [
      { type: 'health', message: 'Cow #134 requires veterinary attention', priority: 'high', icon: '⚠️' },
      { type: 'feeding', message: 'Feed inventory running low', priority: 'medium', icon: '📦' },
      { type: 'breeding', message: '3 animals ready for breeding', priority: 'low', icon: '💝' }
    ],
    performance: {
      milkYield: { current: 1250, target: 1400, unit: 'L/day' },
      weightGain: { current: 1.2, target: 1.5, unit: 'kg/day' },
      feedEfficiency: { current: 85, target: 90, unit: '%' },
      healthScore: { current: 94, target: 95, unit: '%' }
    }
  };

  // Generate beta modules based on access control
  const betaModules: BetaModule[] = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <Dashboard />,
      description: `${livestockStats.totalAnimals} Animals • ${livestockStats.healthyAnimals} Healthy • ${livestockStats.milkProduction}L Milk/day`,
      isLocked: !hasModuleAccess(authUser, 'dashboard'),
      isPremium: moduleAccessConfig.dashboard.isPremiumFeature,
      color: '#2196F3',
      route: '/dashboard',
      backgroundImage: '/images/dashboard/main-dashboard.jpg'
    },
    {
      id: 'animals',
      name: 'Animal Management',
      icon: <Pets />,
      description: `${livestockStats.totalAnimals} Total • ${livestockStats.breeds.length} Breeds • ${livestockStats.newBorns} New Births (Beta: 50 max)`,
      isLocked: !hasModuleAccess(authUser, 'animals'),
      isPremium: moduleAccessConfig.animals.isPremiumFeature,
      color: '#4CAF50',
      route: '/dashboard/animals',
      backgroundImage: '/images/modules/animals/cattle-2.avif'
    },
    {
      id: 'health',
      name: 'Health Monitoring',
      icon: <LocalHospital />,
      description: 'Basic health tracking (Beta Limited)',
      isLocked: !hasModuleAccess(authUser, 'health'),
      isPremium: moduleAccessConfig.health.isPremiumFeature,
      color: '#F44336',
      route: '/dashboard/health',
      backgroundImage: '/images/modules/health/veterinary-1.jpg'
    },
    {
      id: 'resources',
      name: 'Resources & Information',
      icon: <Visibility />,
      description: 'Government resources, auctions, information',
      isLocked: !hasModuleAccess(authUser, 'resources'),
      isPremium: moduleAccessConfig.resources.isPremiumFeature,
      color: '#00BCD4',
      route: '/dashboard/resources',
      backgroundImage: '/images/modules/resources/resources-1.jpg'
    },
    {
      id: 'breeding',
      name: 'Breeding Management',
      icon: <TrendingUp />,
      description: 'Advanced breeding analytics & records',
      isLocked: !hasModuleAccess(authUser, 'breeding'),
      isPremium: moduleAccessConfig.breeding.isPremiumFeature,
      color: '#9C27B0',
      backgroundImage: '/images/modules/animals/cattle-3.jpeg'
    },
    {
      id: 'financial',
      name: 'Financial Management',
      icon: <AccountBalance />,
      description: 'Complete financial tracking & reports',
      isLocked: !hasModuleAccess(authUser, 'financial'),
      isPremium: moduleAccessConfig.financial.isPremiumFeature,
      color: '#FF9800',
      backgroundImage: '/images/modules/commercial/commercial-1.jpeg'
    },
    {
      id: 'inventory',
      name: 'Inventory Management',
      icon: <Inventory />,
      description: 'Smart inventory with auto-reordering',
      isLocked: !hasModuleAccess(authUser, 'inventory'),
      isPremium: moduleAccessConfig.inventory.isPremiumFeature,
      color: '#607D8B',
      backgroundImage: '/images/modules/feeding/feed-main.jpeg'
    },
    {
      id: 'analytics',
      name: 'AI Analytics',
      icon: <Assessment />,
      description: 'Predictive insights & recommendations',
      isLocked: !hasModuleAccess(authUser, 'analytics'),
      isPremium: moduleAccessConfig.analytics.isPremiumFeature,
      color: '#E91E63',
      backgroundImage: '/images/modules/rfid/rfid-3.jpg'
    },
    {
      id: 'compliance',
      name: 'Compliance Tracking',
      icon: <Security />,
      description: 'Regulatory compliance management',
      isLocked: !hasModuleAccess(authUser, 'compliance'),
      isPremium: true,
      color: '#795548',
      backgroundImage: '/images/modules/health/veterinary-2.jpg'
    },
    {
      id: 'mobile',
      name: 'Mobile App',
      icon: <NotificationsActive />,
      description: 'iOS & Android applications',
      isLocked: true,
      isPremium: true,
      comingSoon: true,
      color: '#3F51B5',
      backgroundImage: '/images/modules/animals/cattle-4.jpeg'
    }
  ];

  const subscriptionPlans = [
    {
      name: "Professional",
      price: "R299",
      duration: "per month",
      description: "Perfect for growing farms",
      features: [
        "Up to 500 animals",
        "All modules unlocked",
        "Advanced analytics",
        "Priority support",
        "Mobile app access",
        "Data export"
      ],
      color: "#2196F3",
      popular: true,
      savings: "Save 30% vs Beta limits"
    },
    {
      name: "Enterprise",
      price: "R599",
      duration: "per month",
      description: "For large operations",
      features: [
        "Unlimited animals",
        "AI-powered insights",
        "Custom integrations",
        "24/7 support",
        "Multi-farm management",
        "Advanced compliance"
      ],
      color: "#FF9800",
      popular: false,
      savings: "Best value for scale"
    }
  ];

  const handleModuleClick = (module: BetaModule) => {
    if (module.isLocked) {
      setSelectedModule(module);
      setShowUpgradeDialog(true);
    } else if (module.route) {
      // Navigate to the module route
      navigate(module.route);
    } else {
      // Fallback for modules without routes
      console.log(`Opening ${module.name} module`);
      // Show notification for modules without routes
      alert(`${module.name} module is available but route not configured yet.`);
    }
  };

  const handleUpgrade = (planName: string) => {
    navigate('/register', { state: { selectedPlan: planName } });
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setProfileAnchor(event.currentTarget);
    setShowProfileMenu(true);
  };

  const getTrialStatus = () => {
    if (user.trialDaysLeft <= 3) return 'error';
    if (user.trialDaysLeft <= 7) return 'warning';
    return 'info';
  };

  return (
    <Box sx={{ minHeight: '100vh', position: 'relative' }}>
      {/* Life & Nature Background */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: lifeNatureDesign.gradients.pageBackground,
          zIndex: 0
        }}
      />

      {/* Top Navigation */}
      <AppBar
        position="sticky"
        sx={{
          background: alpha(lifeNatureDesign.colors.primary, 0.95),
          backdropFilter: 'blur(30px)',
          borderBottom: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
          zIndex: 1300,
          boxShadow: `0 4px 20px ${alpha(lifeNatureDesign.colors.primaryDark, 0.3)}`
        }}
      >
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                background: lifeNatureDesign.gradients.primary,
                borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                p: 1.5,
                mr: 2,
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <Agriculture sx={{ color: 'white', fontSize: 28 }} />
            </Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 900,
                color: lifeNatureDesign.colors.text,
                background: lifeNatureDesign.gradients.life,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}
            >
              AgriIntel
            </Typography>
            <Typography
              variant="subtitle1"
              sx={{
                ml: 1,
                color: lifeNatureDesign.colors.textAccent,
                fontWeight: 500,
                fontStyle: 'italic'
              }}
            >
              Smart Farming, Smarter Decisions
            </Typography>
            <Chip
              label="BETA"
              size="small"
              sx={{
                ml: 2,
                background: lifeNatureDesign.gradients.harvest,
                color: 'white',
                fontWeight: 'bold',
                borderRadius: lifeNatureDesign.shapes.borderRadius.small
              }}
            />
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <LanguageSelector />
            
            <Badge badgeContent={user.trialDaysLeft} color="error">
              <Timer sx={{ color: theme.palette.text.secondary }} />
            </Badge>
            
            <IconButton onClick={handleProfileClick}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.primary.main }}>
                {user.name.charAt(0)}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Profile Menu */}
      <Menu
        anchorEl={profileAnchor}
        open={showProfileMenu}
        onClose={() => setShowProfileMenu(false)}
        PaperProps={{
          sx: {
            borderRadius: 2,
            minWidth: 200,
            background: alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <MenuItem onClick={() => navigate('/login')}>
          <Person sx={{ mr: 1 }} /> Profile
        </MenuItem>
        <MenuItem onClick={() => setShowUpgradeDialog(true)}>
          <Upgrade sx={{ mr: 1 }} /> Upgrade
        </MenuItem>
        <MenuItem onClick={() => navigate('/')}>
          <ExitToApp sx={{ mr: 1 }} /> Logout
        </MenuItem>
      </Menu>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Trial Status Banner */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Alert 
            severity={getTrialStatus()}
            sx={{ 
              mb: 4, 
              borderRadius: 2,
              background: alpha(theme.palette.background.paper, 0.9),
              backdropFilter: 'blur(10px)'
            }}
            action={
              <Button 
                color="inherit" 
                size="small" 
                onClick={() => setShowUpgradeDialog(true)}
                sx={{ fontWeight: 'bold' }}
              >
                Upgrade Now
              </Button>
            }
          >
            <Typography variant="body1" fontWeight="bold">
              {user.trialDaysLeft <= 3
                ? `⚠️ Trial expires in ${user.trialDaysLeft} days! Upgrade to keep your data.`
                : `🎉 Welcome to AgriIntel Beta! ${user.trialDaysLeft} days remaining in your trial.`
              }
            </Typography>
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Animals: {user.animalsCount}/{user.maxAnimals} • 
                Upgrade for unlimited animals and premium features
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={(user.animalsCount / user.maxAnimals) * 100}
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
              />
            </Box>
          </Alert>
        </motion.div>

        {/* Enhanced AgriIntel Branding & Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            {/* Main AgriIntel Brand */}
            <Typography
              variant="h1"
              sx={{
                fontWeight: 900,
                fontSize: { xs: '3rem', md: '5rem', lg: '6rem' },
                background: lifeNatureDesign.gradients.life,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 4px 8px rgba(0,0,0,0.3)',
                mb: 2,
                letterSpacing: '-0.02em'
              }}
            >
              AgriIntel
            </Typography>

            {/* Slogan */}
            <Typography
              variant="h4"
              sx={{
                fontWeight: 600,
                color: lifeNatureDesign.colors.textAccent,
                mb: 1,
                fontStyle: 'italic',
                textShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            >
              Smart Farming, Smarter Decisions
            </Typography>

            {/* Description */}
            <Typography
              variant="h6"
              sx={{
                color: alpha(lifeNatureDesign.colors.text, 0.9),
                maxWidth: 800,
                mx: 'auto',
                mb: 3,
                lineHeight: 1.6,
                textShadow: '0 1px 2px rgba(0,0,0,0.3)'
              }}
            >
              🌾 The Future of Intelligent Livestock Management
            </Typography>

            {/* Who We Are */}
            <Box sx={{
              background: alpha(lifeNatureDesign.colors.surface, 0.8),
              backdropFilter: 'blur(20px)',
              borderRadius: lifeNatureDesign.shapes.borderRadius.large,
              p: 4,
              maxWidth: 900,
              mx: 'auto',
              border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.3)}`
            }}>
              <Typography
                variant="h5"
                sx={{
                  fontWeight: 700,
                  color: lifeNatureDesign.colors.text,
                  mb: 2
                }}
              >
                🚀 Who We Are
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: lifeNatureDesign.colors.textSecondary,
                  fontSize: '1.1rem',
                  lineHeight: 1.7,
                  mb: 2
                }}
              >
                AgriIntel is South Africa's leading agricultural technology platform, empowering farmers with
                cutting-edge livestock management solutions. We combine artificial intelligence, IoT sensors,
                and data analytics to revolutionize how you manage your farm.
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: lifeNatureDesign.colors.textSecondary,
                  fontSize: '1.1rem',
                  lineHeight: 1.7
                }}
              >
                From small-scale farmers to large commercial operations, we're nurturing the future of
                sustainable agriculture across the continent. 🌍
              </Typography>
            </Box>
          </Box>
        </motion.div>

        {/* Livestock Stats Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Box sx={{ mb: 6 }}>
            <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: 'white', textAlign: 'center' }}>
              🐄 Live Farm Statistics
            </Typography>

            {/* Key Performance Indicators */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: lifeNatureDesign.gradients.primary,
                  color: 'white',
                  borderRadius: lifeNatureDesign.shapes.borderRadius.large,
                  p: 3,
                  textAlign: 'center',
                  backdropFilter: 'blur(20px)',
                  boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.primary, 0.3)}`,
                  border: `1px solid ${alpha(lifeNatureDesign.colors.accent, 0.2)}`,
                  transform: 'scale(1.02)'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.totalAnimals}
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Total Animals</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    {livestockStats.healthyAnimals} Healthy ({Math.round((livestockStats.healthyAnimals/livestockStats.totalAnimals)*100)}%)
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: lifeNatureDesign.gradients.secondary,
                  color: 'white',
                  borderRadius: lifeNatureDesign.shapes.borderRadius.large,
                  p: 3,
                  textAlign: 'center',
                  backdropFilter: 'blur(20px)',
                  boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.secondary, 0.3)}`,
                  border: `1px solid ${alpha(lifeNatureDesign.colors.accentBright, 0.2)}`,
                  transform: 'scale(1.02)'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.milkProduction}L
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Daily Milk Production</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    Target: 1400L ({Math.round((livestockStats.milkProduction/1400)*100)}%)
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: lifeNatureDesign.gradients.harvest,
                  color: 'white',
                  borderRadius: lifeNatureDesign.shapes.borderRadius.large,
                  p: 3,
                  textAlign: 'center',
                  backdropFilter: 'blur(20px)',
                  boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.accentGold, 0.3)}`,
                  border: `1px solid ${alpha(lifeNatureDesign.colors.accentGold, 0.2)}`,
                  transform: 'scale(1.02)'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.pregnantAnimals}
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Pregnant Animals</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    {livestockStats.newBorns} births this month
                  </Typography>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card sx={{
                  background: lifeNatureDesign.gradients.accent,
                  color: 'white',
                  borderRadius: lifeNatureDesign.shapes.borderRadius.large,
                  p: 3,
                  textAlign: 'center',
                  backdropFilter: 'blur(20px)',
                  boxShadow: `0 8px 32px ${alpha(lifeNatureDesign.colors.accent, 0.3)}`,
                  border: `1px solid ${alpha(lifeNatureDesign.colors.accentBright, 0.2)}`,
                  transform: 'scale(1.02)'
                }}>
                  <Typography variant="h2" sx={{ fontWeight: 'bold', mb: 1 }}>
                    {livestockStats.avgWeight}kg
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Average Weight</Typography>
                  <Typography variant="body2" sx={{ opacity: 0.9, mt: 1 }}>
                    +1.2kg/day growth rate
                  </Typography>
                </Card>
              </Grid>
            </Grid>

            {/* Recent Activities & Alerts */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3
                }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    📊 Recent Activities
                  </Typography>
                  {livestockStats.recentActivities.map((activity, index) => (
                    <Box key={index} sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1.5,
                      p: 1,
                      borderRadius: 2,
                      background: alpha(theme.palette.primary.main, 0.05)
                    }}>
                      <Typography sx={{ fontSize: '1.2rem', mr: 2 }}>
                        {activity.icon}
                      </Typography>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {activity.animal}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {activity.type.replace('_', ' ')} • {activity.time}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card sx={{
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  borderRadius: 3,
                  p: 3
                }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                    🚨 Active Alerts
                  </Typography>
                  {livestockStats.alerts.map((alert, index) => (
                    <Box key={index} sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 1.5,
                      p: 1,
                      borderRadius: 2,
                      background: alert.priority === 'high' ? alpha('#f44336', 0.1) :
                                 alert.priority === 'medium' ? alpha('#ff9800', 0.1) :
                                 alpha('#4caf50', 0.1)
                    }}>
                      <Typography sx={{ fontSize: '1.2rem', mr: 2 }}>
                        {alert.icon}
                      </Typography>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          {alert.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Priority: {alert.priority}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Card>
              </Grid>
            </Grid>
          </Box>
        </motion.div>

        {/* Modern Connected Modules Grid */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: 'repeat(2, 1fr)',
              md: 'repeat(3, 1fr)',
              lg: 'repeat(4, 1fr)'
            },
            gap: { xs: 4, md: 6 },
            mt: 6,
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: '20%',
              left: '10%',
              right: '10%',
              height: '3px',
              background: `linear-gradient(90deg, transparent, ${alpha(lifeNatureDesign.colors.accent, 0.4)}, transparent)`,
              borderRadius: '2px',
              zIndex: 0
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              top: '60%',
              left: '15%',
              right: '15%',
              height: '2px',
              background: `linear-gradient(90deg, transparent, ${alpha(lifeNatureDesign.colors.accentBright, 0.3)}, transparent)`,
              borderRadius: '1px',
              zIndex: 0
            }
          }}
        >
          {betaModules.map((module, index) => {
            const sizes = ['medium', 'large', 'medium', 'small', 'medium', 'large', 'medium', 'medium'];
            const cardSize = sizes[index % sizes.length] as any;

            return (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                style={{
                  position: 'relative',
                  zIndex: 1,
                  justifySelf: index % 3 === 1 ? 'center' : index % 3 === 2 ? 'end' : 'start'
                }}
              >
                <ModernConnectedCard
                  id={module.id}
                  name={module.name}
                  description={module.description}
                  icon={module.icon}
                  backgroundImage={module.backgroundImage}
                  color={module.color}
                  isLocked={module.isLocked}
                  isPremium={module.isPremium}
                  comingSoon={module.comingSoon}
                  onClick={() => handleModuleClick(module)}
                  index={index}
                  size={cardSize}
                />
              </motion.div>
            );
          })}
        </Box>

        {/* Upgrade CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,255,255,0.2)',
                p: 4
              }}
            >
              <Typography variant="h5" sx={{ color: 'white', fontWeight: 'bold', mb: 2 }}>
                Ready to unlock the full potential?
              </Typography>
              <Typography variant="body1" sx={{ color: alpha('#fff', 0.9), mb: 3 }}>
                Upgrade to Professional or Enterprise for unlimited animals, advanced analytics, and premium support.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Star />}
                  onClick={() => setShowUpgradeDialog(true)}
                  sx={{
                    background: 'linear-gradient(45deg, #FF9800 30%, #F57C00 90%)',
                    boxShadow: '0 3px 5px 2px rgba(255, 152, 0, .3)',
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem'
                  }}
                >
                  Upgrade Now
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<Visibility />}
                  onClick={() => setShowImageShowcase(true)}
                  sx={{
                    borderColor: lifeNatureDesign.colors.accent,
                    color: lifeNatureDesign.colors.accent,
                    px: 4,
                    py: 1.5,
                    fontSize: '1.1rem',
                    '&:hover': {
                      borderColor: lifeNatureDesign.colors.accentBright,
                      background: alpha(lifeNatureDesign.colors.accent, 0.1)
                    }
                  }}
                >
                  View Gallery
                </Button>
              </Box>
            </Card>
          </Box>
        </motion.div>
      </Container>

      {/* Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.9))',
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            Unlock Premium Features
          </Typography>
          {selectedModule && (
            <Typography variant="body1" color="text.secondary">
              {selectedModule.name} requires a premium subscription
            </Typography>
          )}
        </DialogTitle>

        <DialogContent>
          <Grid container spacing={3}>
            {subscriptionPlans.map((plan) => (
              <Grid item xs={12} md={6} key={plan.name}>
                <Card
                  sx={{
                    position: 'relative',
                    border: plan.popular ? `3px solid ${plan.color}` : '1px solid',
                    borderColor: plan.popular ? plan.color : 'divider',
                    transform: plan.popular ? 'scale(1.02)' : 'scale(1)',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {plan.popular && (
                    <Chip
                      label="Most Popular"
                      sx={{
                        position: 'absolute',
                        top: -12,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        background: `linear-gradient(45deg, ${plan.color}, ${plan.color}80)`,
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  )}

                  <CardContent sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                      {plan.name}
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Typography
                        variant="h3"
                        sx={{
                          fontWeight: 'bold',
                          color: plan.color,
                          display: 'inline'
                        }}
                      >
                        {plan.price}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        {plan.duration}
                      </Typography>
                    </Box>

                    <Chip
                      label={plan.savings}
                      size="small"
                      sx={{
                        background: alpha(plan.color, 0.1),
                        color: plan.color,
                        mb: 2
                      }}
                    />

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      {plan.description}
                    </Typography>

                    <Box sx={{ mb: 3, textAlign: 'left' }}>
                      {plan.features.map((feature, index) => (
                        <Box
                          key={index}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            mb: 1
                          }}
                        >
                          <Star
                            sx={{
                              color: plan.color,
                              fontSize: 16,
                              mr: 1
                            }}
                          />
                          <Typography variant="body2">
                            {feature}
                          </Typography>
                        </Box>
                      ))}
                    </Box>

                    <Button
                      variant={plan.popular ? "contained" : "outlined"}
                      fullWidth
                      size="large"
                      onClick={() => handleUpgrade(plan.name)}
                      sx={{
                        background: plan.popular
                          ? `linear-gradient(45deg, ${plan.color}, ${plan.color}80)`
                          : 'transparent',
                        borderColor: plan.color,
                        color: plan.popular ? 'white' : plan.color,
                        py: 1.5,
                        fontWeight: 'bold',
                        '&:hover': {
                          background: plan.popular
                            ? `linear-gradient(45deg, ${plan.color}90, ${plan.color}70)`
                            : alpha(plan.color, 0.1)
                        }
                      }}
                    >
                      {plan.popular ? 'Start Free Trial' : 'Choose Plan'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          <Box sx={{ mt: 3, p: 2, background: alpha('#4CAF50', 0.1), borderRadius: 2 }}>
            <Typography variant="body2" sx={{ textAlign: 'center', color: '#2E7D32' }}>
              🎉 <strong>Special Beta Offer:</strong> Get 30% off your first 3 months when you upgrade from beta!
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Continue with Beta
          </Button>
          <Button
            variant="contained"
            onClick={() => navigate('/register')}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)'
            }}
          >
            View All Plans
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Showcase Dialog */}
      <Dialog
        open={showImageShowcase}
        onClose={() => setShowImageShowcase(false)}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(10, 14, 39, 0.98), rgba(26, 31, 58, 0.98))',
            backdropFilter: 'blur(30px)',
            border: `1px solid ${alpha('#00D4AA', 0.3)}`,
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          background: 'linear-gradient(135deg, #00D4AA, #7C3AED)',
          color: 'white',
          m: 0
        }}>
          <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
            🌾 AgriIntel Professional Gallery
          </Typography>
          <IconButton
            onClick={() => setShowImageShowcase(false)}
            sx={{ color: 'white' }}
          >
            <ExitToApp />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
          <ImageShowcase />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default EnhancedBeta;
