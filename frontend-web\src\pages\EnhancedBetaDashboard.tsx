import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  Paper,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  ListItemButton
} from '@mui/material';
import {
  Pets,
  LocalHospital,
  MenuBook,
  Lock,
  Star,
  Menu,
  Close,
  Dashboard,
  Upgrade,
  PlayArrow
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import EnhancedGradientBackground from '../components/common/EnhancedGradientBackground';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';

const EnhancedBetaDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [upgradeDialog, setUpgradeDialog] = useState(false);

  // Beta modules - only these are accessible
  const betaModules = [
    {
      id: 'dashboard',
      title: 'Dashboard Overview',
      icon: Dashboard,
      description: 'View your farm overview and key metrics',
      path: '/beta/dashboard',
      available: true,
      color: theme.palette.primary.main
    },
    {
      id: 'animals',
      title: 'Animal Management',
      icon: Pets,
      description: 'Manage up to 50 animals with basic records',
      path: '/beta/animals',
      available: true,
      color: '#4caf50'
    },
    {
      id: 'health',
      title: 'Animal Health',
      icon: LocalHospital,
      description: 'Basic health monitoring and vaccination tracking',
      path: '/beta/health',
      available: true,
      color: '#f44336'
    },
    {
      id: 'resources',
      title: 'Resources & Information',
      icon: MenuBook,
      description: 'Government resources, auctions, and information',
      path: '/beta/resources',
      available: true,
      color: '#ff9800'
    }
  ];

  // Locked premium modules
  const premiumModules = [
    {
      id: 'breeding',
      title: 'Breeding Management',
      icon: Lock,
      description: 'Advanced breeding optimization and genetics',
      premium: true,
      savings: 'R5,000/month'
    },
    {
      id: 'feeding',
      title: 'Feed Management',
      icon: Lock,
      description: 'Automated feeding schedules and nutrition optimization',
      premium: true,
      savings: 'R3,000/month'
    },
    {
      id: 'financial',
      title: 'Financial Management',
      icon: Lock,
      description: 'Complete P&L analysis and tax optimization',
      premium: true,
      savings: 'R8,000/month'
    },
    {
      id: 'inventory',
      title: 'Inventory Management',
      icon: Lock,
      description: 'Smart inventory tracking and automated ordering',
      premium: true,
      savings: 'R2,500/month'
    },
    {
      id: 'commercial',
      title: 'Commercial Operations',
      icon: Lock,
      description: 'Market analysis and commercial optimization',
      premium: true,
      savings: 'R6,000/month'
    },
    {
      id: 'reports',
      title: 'Advanced Reports',
      icon: Lock,
      description: 'AI-powered insights and predictive analytics',
      premium: true,
      savings: 'R4,000/month'
    },
    {
      id: 'compliance',
      title: 'Compliance Management',
      icon: Lock,
      description: 'Automated compliance tracking and reporting',
      premium: true,
      savings: 'R3,500/month'
    },
    {
      id: 'settings',
      title: 'Advanced Settings',
      icon: Lock,
      description: 'Multi-user management and advanced configurations',
      premium: true,
      savings: 'Priceless'
    }
  ];

  const handleModuleClick = (module: any) => {
    if (module.available) {
      navigate(module.path);
    } else {
      setUpgradeDialog(true);
    }
  };

  const sidebarContent = (
    <Box sx={{ width: 280, p: 2 }}>
      <Box sx={{ mb: 3 }}>
        <AgriIntelBrand variant="compact" size="medium" />
        <Chip 
          label="BETA VERSION" 
          color="primary" 
          size="small" 
          sx={{ mt: 1, fontSize: '0.7rem' }}
        />
      </Box>

      <Typography variant="h6" sx={{ mb: 2, color: 'success.main' }}>
        Available Modules
      </Typography>
      
      <List>
        {betaModules.map((module) => (
          <ListItemButton
            key={module.id}
            onClick={() => handleModuleClick(module)}
            sx={{ 
              borderRadius: 2, 
              mb: 1,
              '&:hover': {
                backgroundColor: alpha(module.color, 0.1)
              }
            }}
          >
            <ListItemIcon>
              <module.icon sx={{ color: module.color }} />
            </ListItemIcon>
            <ListItemText 
              primary={module.title}
              secondary={module.description}
            />
          </ListItemButton>
        ))}
      </List>

      <Typography variant="h6" sx={{ mb: 2, mt: 3, color: 'warning.main' }}>
        Premium Modules
      </Typography>
      
      <List>
        {premiumModules.map((module) => (
          <ListItemButton
            key={module.id}
            onClick={() => setUpgradeDialog(true)}
            sx={{ 
              borderRadius: 2, 
              mb: 1,
              opacity: 0.6,
              '&:hover': {
                backgroundColor: alpha(theme.palette.warning.main, 0.1),
                opacity: 0.8
              }
            }}
          >
            <ListItemIcon>
              <Lock sx={{ color: 'warning.main' }} />
            </ListItemIcon>
            <ListItemText 
              primary={module.title}
              secondary={`🔒 Premium - Save ${module.savings}`}
            />
          </ListItemButton>
        ))}
      </List>

      <Button
        variant="contained"
        fullWidth
        onClick={() => setUpgradeDialog(true)}
        sx={{ mt: 3 }}
        startIcon={<Upgrade />}
      >
        Upgrade to Premium
      </Button>
    </Box>
  );

  return (
    <EnhancedGradientBackground
      module="beta"
      variant="mesh"
      enableAnimation={true}
      enableParticles={true}
      opacity={0.7}
    >
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            onClick={() => setSidebarOpen(true)}
            sx={{ mr: 2 }}
          >
            <Menu />
          </IconButton>
          
          <AgriIntelBrand variant="compact" size="small" color="white" />
          
          <Chip 
            label="BETA" 
            color="secondary" 
            size="small" 
            sx={{ ml: 2 }}
          />
          
          <Box sx={{ flexGrow: 1 }} />
          
          <Button
            color="inherit"
            onClick={() => setUpgradeDialog(true)}
            startIcon={<Star />}
          >
            Upgrade
          </Button>
          
          <Button
            color="inherit"
            onClick={() => navigate('/')}
          >
            Exit Beta
          </Button>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        anchor="left"
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      >
        {sidebarContent}
      </Drawer>

      {/* Main Content */}
      <Container maxWidth="xl" sx={{ mt: 10, py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Welcome Section */}
          <Paper sx={{ p: 4, mb: 4, borderRadius: 3 }}>
            <Typography variant="h3" fontWeight="bold" sx={{ mb: 2 }}>
              Welcome to AgriIntel Beta! 🌾
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
              You're experiencing the future of livestock management. 
              Explore our beta features and see how AgriIntel can transform your farm.
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body1">
                <strong>Beta Access Includes:</strong> Animal Management (50 animals), 
                Basic Health Monitoring, Government Resources, and Dashboard Overview.
              </Typography>
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/beta/animals')}
                startIcon={<PlayArrow />}
              >
                Start Managing Animals
              </Button>
              <Button
                variant="outlined"
                size="large"
                onClick={() => setUpgradeDialog(true)}
                startIcon={<Upgrade />}
              >
                See Premium Features
              </Button>
            </Box>
          </Paper>

          {/* Available Modules Grid */}
          <Typography variant="h4" fontWeight="bold" sx={{ mb: 3 }}>
            Available Beta Modules
          </Typography>
          
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {betaModules.map((module, index) => (
              <Grid item xs={12} sm={6} md={3} key={module.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card 
                    sx={{ 
                      height: '100%', 
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: `0 12px 24px ${alpha(module.color, 0.2)}`
                      }
                    }}
                    onClick={() => handleModuleClick(module)}
                  >
                    <CardContent sx={{ p: 3, textAlign: 'center' }}>
                      <Box
                        sx={{
                          p: 2,
                          borderRadius: 3,
                          backgroundColor: alpha(module.color, 0.1),
                          display: 'inline-block',
                          mb: 2
                        }}
                      >
                        <module.icon sx={{ fontSize: 40, color: module.color }} />
                      </Box>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                        {module.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {module.description}
                      </Typography>
                      <Chip 
                        label="Available" 
                        color="success" 
                        size="small" 
                        sx={{ mt: 2 }}
                      />
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      </Container>

      {/* Upgrade Dialog */}
      <Dialog 
        open={upgradeDialog} 
        onClose={() => setUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            🚀 Upgrade to Premium
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Unlock all premium features and save up to R32,000 per month with advanced 
            livestock management capabilities.
          </Typography>
          
          <Grid container spacing={2}>
            {premiumModules.slice(0, 4).map((module) => (
              <Grid item xs={12} sm={6} key={module.id}>
                <Card sx={{ p: 2 }}>
                  <Typography variant="h6" fontWeight="bold">
                    {module.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    {module.description}
                  </Typography>
                  <Chip 
                    label={`Save ${module.savings}`} 
                    color="success" 
                    size="small"
                  />
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUpgradeDialog(false)}>
            Maybe Later
          </Button>
          <Button 
            variant="contained" 
            onClick={() => navigate('/register')}
            startIcon={<Star />}
          >
            Upgrade Now - First Month Free!
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedGradientBackground>
  );
};

export default EnhancedBetaDashboard;
