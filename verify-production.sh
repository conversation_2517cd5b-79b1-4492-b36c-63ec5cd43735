#!/bin/bash

# AgriIntel Production Verification Script
# Comprehensive verification of production deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOMAIN="agriintel.com"
API_URL="https://$DOMAIN/api"
HEALTH_URL="https://$DOMAIN/health"
BACKEND_PORT=3002

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_CHECKS++))
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNING_CHECKS++))
}

error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_CHECKS++))
}

check() {
    ((TOTAL_CHECKS++))
}

# System checks
check_system_resources() {
    log "Checking system resources..."
    
    # Memory check
    check
    MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
    if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
        success "Memory usage: ${MEMORY_USAGE}%"
    elif (( $(echo "$MEMORY_USAGE < 90" | bc -l) )); then
        warning "Memory usage: ${MEMORY_USAGE}% (consider monitoring)"
    else
        error "Memory usage: ${MEMORY_USAGE}% (critical)"
    fi
    
    # Disk space check
    check
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -lt 80 ]; then
        success "Disk usage: ${DISK_USAGE}%"
    elif [ "$DISK_USAGE" -lt 90 ]; then
        warning "Disk usage: ${DISK_USAGE}% (consider cleanup)"
    else
        error "Disk usage: ${DISK_USAGE}% (critical)"
    fi
    
    # CPU load check
    check
    CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    CPU_CORES=$(nproc)
    if (( $(echo "$CPU_LOAD < $CPU_CORES" | bc -l) )); then
        success "CPU load: $CPU_LOAD (${CPU_CORES} cores)"
    else
        warning "CPU load: $CPU_LOAD (${CPU_CORES} cores) - high load"
    fi
}

# Service checks
check_services() {
    log "Checking system services..."
    
    # AgriIntel service
    check
    if systemctl is-active --quiet agriintel; then
        success "AgriIntel service is running"
    else
        error "AgriIntel service is not running"
    fi
    
    # Nginx service
    check
    if systemctl is-active --quiet nginx; then
        success "Nginx service is running"
    else
        error "Nginx service is not running"
    fi
    
    # Port checks
    check
    if netstat -tulpn | grep -q ":$BACKEND_PORT"; then
        success "Backend port $BACKEND_PORT is listening"
    else
        error "Backend port $BACKEND_PORT is not listening"
    fi
    
    check
    if netstat -tulpn | grep -q ":443"; then
        success "HTTPS port 443 is listening"
    else
        error "HTTPS port 443 is not listening"
    fi
}

# SSL certificate checks
check_ssl() {
    log "Checking SSL certificate..."
    
    check
    if command -v openssl &> /dev/null; then
        CERT_EXPIRY=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        EXPIRY_TIMESTAMP=$(date -d "$CERT_EXPIRY" +%s)
        CURRENT_TIMESTAMP=$(date +%s)
        DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))
        
        if [ "$DAYS_UNTIL_EXPIRY" -gt 30 ]; then
            success "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
        elif [ "$DAYS_UNTIL_EXPIRY" -gt 7 ]; then
            warning "SSL certificate expires in $DAYS_UNTIL_EXPIRY days (renewal recommended)"
        else
            error "SSL certificate expires in $DAYS_UNTIL_EXPIRY days (urgent renewal needed)"
        fi
    else
        warning "OpenSSL not available for certificate check"
    fi
}

# Network connectivity checks
check_connectivity() {
    log "Checking network connectivity..."
    
    # Domain resolution
    check
    if nslookup $DOMAIN &> /dev/null; then
        success "Domain $DOMAIN resolves correctly"
    else
        error "Domain $DOMAIN does not resolve"
    fi
    
    # HTTPS connectivity
    check
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN" | grep -q "200\|301\|302"; then
        success "HTTPS connectivity to $DOMAIN working"
    else
        error "HTTPS connectivity to $DOMAIN failed"
    fi
    
    # Health endpoint
    check
    HEALTH_RESPONSE=$(curl -s -w "%{http_code}" "$HEALTH_URL" -o /tmp/health_response.json)
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        success "Health endpoint responding correctly"
    else
        error "Health endpoint returned status: $HEALTH_RESPONSE"
    fi
}

# API endpoint checks
check_api_endpoints() {
    log "Checking API endpoints..."
    
    local endpoints=(
        "/api/animals"
        "/api/health"
        "/api/breeding"
        "/api/financial"
        "/api/inventory"
        "/api/resources"
    )
    
    for endpoint in "${endpoints[@]}"; do
        check
        RESPONSE=$(curl -s -w "%{http_code}" "${API_URL}${endpoint}" -o /dev/null)
        if [ "$RESPONSE" = "200" ] || [ "$RESPONSE" = "401" ]; then
            success "API endpoint $endpoint responding"
        else
            error "API endpoint $endpoint returned status: $RESPONSE"
        fi
    done
}

# Authentication checks
check_authentication() {
    log "Checking authentication system..."
    
    # Test demo login
    check
    LOGIN_RESPONSE=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"Demo","password":"123"}' \
        "${API_URL}/auth/login" \
        -o /tmp/login_response.json)
    
    if [ "$LOGIN_RESPONSE" = "200" ]; then
        success "Demo user authentication working"
    else
        error "Demo user authentication failed (status: $LOGIN_RESPONSE)"
    fi
    
    # Test admin login
    check
    ADMIN_LOGIN_RESPONSE=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"Admin@123"}' \
        "${API_URL}/auth/login" \
        -o /tmp/admin_login_response.json)
    
    if [ "$ADMIN_LOGIN_RESPONSE" = "200" ]; then
        success "Admin user authentication working"
    else
        error "Admin user authentication failed (status: $ADMIN_LOGIN_RESPONSE)"
    fi
}

# Database connectivity check
check_database() {
    log "Checking database connectivity..."
    
    check
    if [ -n "$MONGODB_URI" ]; then
        # Test MongoDB connection using mongosh if available
        if command -v mongosh &> /dev/null; then
            if mongosh "$MONGODB_URI" --eval "db.runCommand('ping')" &> /dev/null; then
                success "MongoDB connection successful"
            else
                error "MongoDB connection failed"
            fi
        else
            warning "mongosh not available for database check"
        fi
    else
        warning "MONGODB_URI not set in environment"
    fi
}

# Performance checks
check_performance() {
    log "Checking performance metrics..."
    
    # Page load time
    check
    LOAD_TIME=$(curl -s -w "%{time_total}" -o /dev/null "https://$DOMAIN")
    if (( $(echo "$LOAD_TIME < 3.0" | bc -l) )); then
        success "Page load time: ${LOAD_TIME}s"
    elif (( $(echo "$LOAD_TIME < 5.0" | bc -l) )); then
        warning "Page load time: ${LOAD_TIME}s (consider optimization)"
    else
        error "Page load time: ${LOAD_TIME}s (too slow)"
    fi
    
    # API response time
    check
    API_TIME=$(curl -s -w "%{time_total}" -o /dev/null "$HEALTH_URL")
    if (( $(echo "$API_TIME < 0.5" | bc -l) )); then
        success "API response time: ${API_TIME}s"
    elif (( $(echo "$API_TIME < 1.0" | bc -l) )); then
        warning "API response time: ${API_TIME}s (consider optimization)"
    else
        error "API response time: ${API_TIME}s (too slow)"
    fi
}

# Security checks
check_security() {
    log "Checking security configuration..."
    
    # HTTPS redirect
    check
    HTTP_RESPONSE=$(curl -s -w "%{http_code}" -L "http://$DOMAIN" -o /dev/null)
    if [ "$HTTP_RESPONSE" = "200" ]; then
        # Check if final URL is HTTPS
        FINAL_URL=$(curl -s -w "%{url_effective}" -L "http://$DOMAIN" -o /dev/null)
        if [[ "$FINAL_URL" == https://* ]]; then
            success "HTTP to HTTPS redirect working"
        else
            error "HTTP to HTTPS redirect not working"
        fi
    else
        error "HTTP redirect check failed"
    fi
    
    # Security headers
    check
    HEADERS=$(curl -s -I "https://$DOMAIN")
    if echo "$HEADERS" | grep -q "Strict-Transport-Security"; then
        success "HSTS header present"
    else
        warning "HSTS header missing"
    fi
    
    check
    if echo "$HEADERS" | grep -q "X-Content-Type-Options"; then
        success "X-Content-Type-Options header present"
    else
        warning "X-Content-Type-Options header missing"
    fi
}

# Generate summary report
generate_summary() {
    log "Generating verification summary..."
    
    echo ""
    echo "=========================================="
    echo "  AGRIINTEL PRODUCTION VERIFICATION"
    echo "=========================================="
    echo ""
    echo "Total Checks: $TOTAL_CHECKS"
    echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "Warnings: ${YELLOW}$WARNING_CHECKS${NC}"
    echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
    echo ""
    
    PASS_RATE=$(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))
    echo "Pass Rate: $PASS_RATE%"
    echo ""
    
    if [ "$FAILED_CHECKS" -eq 0 ] && [ "$WARNING_CHECKS" -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL CHECKS PASSED - PRODUCTION READY! 🎉${NC}"
    elif [ "$FAILED_CHECKS" -eq 0 ]; then
        echo -e "${YELLOW}⚠️  PRODUCTION READY WITH WARNINGS ⚠️${NC}"
        echo "Consider addressing warnings before full launch"
    else
        echo -e "${RED}❌ PRODUCTION NOT READY ❌${NC}"
        echo "Critical issues must be resolved before launch"
    fi
    
    echo ""
    echo "Verification completed at: $(date)"
    echo "=========================================="
}

# Main verification process
main() {
    log "Starting AgriIntel production verification..."
    echo ""
    
    check_system_resources
    check_services
    check_ssl
    check_connectivity
    check_api_endpoints
    check_authentication
    check_database
    check_performance
    check_security
    
    generate_summary
}

# Run main function
main "$@"
