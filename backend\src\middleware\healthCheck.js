/**
 * Health Check Middleware
 * Comprehensive system health monitoring for production
 */

const mongodb = require('../config/mongodb');
const logger = require('../utils/logger');

class HealthCheckService {
  constructor() {
    this.checks = new Map();
    this.lastResults = new Map();
    this.initializeChecks();
  }

  initializeChecks() {
    // Database connectivity check
    this.checks.set('database', async () => {
      try {
        const db = mongodb.getDB();
        if (!db) {
          throw new Error('Database connection not available');
        }
        
        // Test database operation
        await db.admin().ping();
        
        // Check collections
        const collections = await db.listCollections().toArray();
        const requiredCollections = ['users', 'animals', 'health_records'];
        const missingCollections = requiredCollections.filter(
          col => !collections.find(c => c.name === col)
        );
        
        if (missingCollections.length > 0) {
          throw new Error(`Missing collections: ${missingCollections.join(', ')}`);
        }
        
        return {
          status: 'healthy',
          message: 'Database connection successful',
          details: {
            collections: collections.length,
            ping: 'successful'
          }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          message: `Database check failed: ${error.message}`,
          details: { error: error.message }
        };
      }
    });

    // Memory usage check
    this.checks.set('memory', async () => {
      try {
        const memUsage = process.memoryUsage();
        const totalMem = require('os').totalmem();
        const freeMem = require('os').freemem();
        const usedMem = totalMem - freeMem;
        const memoryUsagePercent = (usedMem / totalMem) * 100;
        
        const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
        const heapTotalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
        const externalMB = Math.round(memUsage.external / 1024 / 1024);
        
        const status = memoryUsagePercent > 90 ? 'unhealthy' : 
                      memoryUsagePercent > 75 ? 'warning' : 'healthy';
        
        return {
          status,
          message: `Memory usage: ${memoryUsagePercent.toFixed(1)}%`,
          details: {
            heapUsed: `${heapUsedMB}MB`,
            heapTotal: `${heapTotalMB}MB`,
            external: `${externalMB}MB`,
            systemMemoryUsage: `${memoryUsagePercent.toFixed(1)}%`
          }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          message: `Memory check failed: ${error.message}`,
          details: { error: error.message }
        };
      }
    });

    // Disk space check
    this.checks.set('disk', async () => {
      try {
        const fs = require('fs').promises;
        const path = require('path');
        
        const stats = await fs.statvfs ? fs.statvfs('.') : null;
        if (!stats) {
          // Fallback for systems without statvfs
          return {
            status: 'warning',
            message: 'Disk space check not available on this system',
            details: { available: 'unknown' }
          };
        }
        
        const totalSpace = stats.blocks * stats.frsize;
        const freeSpace = stats.bavail * stats.frsize;
        const usedSpace = totalSpace - freeSpace;
        const usagePercent = (usedSpace / totalSpace) * 100;
        
        const status = usagePercent > 90 ? 'unhealthy' : 
                      usagePercent > 80 ? 'warning' : 'healthy';
        
        return {
          status,
          message: `Disk usage: ${usagePercent.toFixed(1)}%`,
          details: {
            total: `${Math.round(totalSpace / 1024 / 1024 / 1024)}GB`,
            free: `${Math.round(freeSpace / 1024 / 1024 / 1024)}GB`,
            used: `${Math.round(usedSpace / 1024 / 1024 / 1024)}GB`,
            usagePercent: `${usagePercent.toFixed(1)}%`
          }
        };
      } catch (error) {
        return {
          status: 'warning',
          message: `Disk check failed: ${error.message}`,
          details: { error: error.message }
        };
      }
    });

    // API endpoints check
    this.checks.set('api', async () => {
      try {
        const endpoints = [
          '/api/auth',
          '/api/animals',
          '/api/health',
          '/api/breeding',
          '/api/financial'
        ];
        
        const results = [];
        for (const endpoint of endpoints) {
          try {
            // This is a simplified check - in a real implementation,
            // you might make actual HTTP requests to test endpoints
            results.push({ endpoint, status: 'available' });
          } catch (error) {
            results.push({ endpoint, status: 'error', error: error.message });
          }
        }
        
        const failedEndpoints = results.filter(r => r.status === 'error');
        const status = failedEndpoints.length === 0 ? 'healthy' : 
                      failedEndpoints.length < endpoints.length / 2 ? 'warning' : 'unhealthy';
        
        return {
          status,
          message: `${results.length - failedEndpoints.length}/${results.length} endpoints available`,
          details: { endpoints: results }
        };
      } catch (error) {
        return {
          status: 'unhealthy',
          message: `API check failed: ${error.message}`,
          details: { error: error.message }
        };
      }
    });

    // External dependencies check
    this.checks.set('external', async () => {
      try {
        const dependencies = [];
        
        // Check if external APIs are configured
        if (process.env.WEATHER_API_KEY) {
          dependencies.push({ name: 'Weather API', status: 'configured' });
        }
        
        if (process.env.MAPS_API_KEY) {
          dependencies.push({ name: 'Maps API', status: 'configured' });
        }
        
        if (process.env.SMTP_HOST) {
          dependencies.push({ name: 'Email Service', status: 'configured' });
        }
        
        return {
          status: 'healthy',
          message: `${dependencies.length} external dependencies configured`,
          details: { dependencies }
        };
      } catch (error) {
        return {
          status: 'warning',
          message: `External dependencies check failed: ${error.message}`,
          details: { error: error.message }
        };
      }
    });
  }

  async runCheck(checkName) {
    try {
      const check = this.checks.get(checkName);
      if (!check) {
        throw new Error(`Health check '${checkName}' not found`);
      }
      
      const startTime = Date.now();
      const result = await check();
      const duration = Date.now() - startTime;
      
      const checkResult = {
        ...result,
        timestamp: new Date().toISOString(),
        duration: `${duration}ms`
      };
      
      this.lastResults.set(checkName, checkResult);
      return checkResult;
    } catch (error) {
      const errorResult = {
        status: 'unhealthy',
        message: `Health check error: ${error.message}`,
        timestamp: new Date().toISOString(),
        duration: '0ms',
        details: { error: error.message }
      };
      
      this.lastResults.set(checkName, errorResult);
      return errorResult;
    }
  }

  async runAllChecks() {
    const results = {};
    const checkPromises = Array.from(this.checks.keys()).map(async (checkName) => {
      results[checkName] = await this.runCheck(checkName);
    });
    
    await Promise.all(checkPromises);
    
    // Calculate overall status
    const statuses = Object.values(results).map(r => r.status);
    const overallStatus = statuses.includes('unhealthy') ? 'unhealthy' :
                         statuses.includes('warning') ? 'warning' : 'healthy';
    
    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      summary: {
        total: statuses.length,
        healthy: statuses.filter(s => s === 'healthy').length,
        warning: statuses.filter(s => s === 'warning').length,
        unhealthy: statuses.filter(s => s === 'unhealthy').length
      }
    };
  }

  getLastResults() {
    const results = {};
    for (const [checkName, result] of this.lastResults.entries()) {
      results[checkName] = result;
    }
    return results;
  }
}

// Create singleton instance
const healthCheckService = new HealthCheckService();

// Health check middleware
const healthCheckMiddleware = async (req, res, next) => {
  try {
    const checkName = req.params.check;
    
    if (checkName) {
      // Run specific check
      const result = await healthCheckService.runCheck(checkName);
      res.status(result.status === 'healthy' ? 200 : 503).json(result);
    } else {
      // Run all checks
      const results = await healthCheckService.runAllChecks();
      res.status(results.status === 'healthy' ? 200 : 503).json(results);
    }
  } catch (error) {
    logger.error('Health check middleware error:', error);
    res.status(500).json({
      status: 'unhealthy',
      message: 'Health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// Simple health endpoint
const simpleHealthCheck = (req, res) => {
  res.status(200).json({
    status: 'healthy',
    message: 'AgriIntel API is running',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0-beta',
    uptime: process.uptime()
  });
};

module.exports = {
  healthCheckService,
  healthCheckMiddleware,
  simpleHealthCheck
};
