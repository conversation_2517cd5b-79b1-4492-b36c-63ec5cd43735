import React from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Conte<PERSON>,
  <PERSON>rid,
  Chip,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import {
  Lock,
  Upgrade,
  Star,
  CheckCircle,
  ArrowBack
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface ModuleGuardProps {
  children: React.ReactNode;
  module: string;
  requiredRole?: 'beta' | 'professional' | 'enterprise' | 'admin';
}

const ModuleGuard: React.FC<ModuleGuardProps> = ({
  children,
  module,
  requiredRole = 'professional'
}) => {
  const { 
    user, 
    canAccessModule, 
    getAccessLevel,
    isBetaUser 
  } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();

  // Module configuration
  const moduleConfig: Record<string, {
    name: string;
    description: string;
    icon: string;
    features: string[];
    benefits: string[];
    requiredPlan: string;
    savings: string;
    color: string;
  }> = {
    breeding: {
      name: 'Breeding Management',
      description: 'Optimize your breeding program with AI-powered insights and genetic tracking',
      icon: '🐄',
      features: [
        'Breeding schedule optimization',
        'Genetic lineage tracking',
        'Pregnancy monitoring & alerts',
        'Birth predictions with AI',
        'Breeding performance analytics',
        'Heat detection calendar',
        'Sire selection recommendations'
      ],
      benefits: [
        'Increase conception rates by 35%',
        'Reduce breeding costs by R5,000/month',
        'Improve genetic quality over time',
        'Minimize breeding-related losses'
      ],
      requiredPlan: 'Professional',
      savings: 'Save R60,000+ annually',
      color: '#e91e63'
    },
    financial: {
      name: 'Financial Management',
      description: 'Complete financial control with automated tracking and tax optimization',
      icon: '💰',
      features: [
        'Automated expense tracking',
        'Profit & Loss analysis',
        'ROI calculations per animal',
        'Tax optimization strategies',
        'Banking API integration',
        'Financial forecasting',
        'Budget planning & alerts'
      ],
      benefits: [
        'Save R15,000+ in tax optimization',
        'Reduce accounting costs by 70%',
        'Improve cash flow management',
        'Make data-driven financial decisions'
      ],
      requiredPlan: 'Professional',
      savings: 'Save R180,000+ annually',
      color: '#4caf50'
    },
    inventory: {
      name: 'Inventory Management',
      description: 'Smart inventory control with automated ordering and waste reduction',
      icon: '📦',
      features: [
        'Real-time inventory tracking',
        'Automated reorder points',
        'Supplier price comparison',
        'Waste reduction analytics',
        'Feed optimization',
        'Equipment maintenance tracking',
        'Cost optimization algorithms'
      ],
      benefits: [
        'Reduce inventory costs by 25%',
        'Eliminate stockouts and waste',
        'Optimize feed conversion ratios',
        'Streamline supplier relationships'
      ],
      requiredPlan: 'Professional',
      savings: 'Save R120,000+ annually',
      color: '#ff9800'
    },
    commercial: {
      name: 'Commercial Operations',
      description: 'Maximize revenue with market intelligence and sales optimization',
      icon: '🏪',
      features: [
        'Live market price tracking',
        'Auction integration (BKB, etc.)',
        'Sales optimization algorithms',
        'Customer relationship management',
        'Revenue forecasting',
        'Market trend analysis',
        'Pricing strategy recommendations'
      ],
      benefits: [
        'Increase sales revenue by 20%',
        'Get best market prices',
        'Reduce sales transaction costs',
        'Build stronger customer relationships'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Increase revenue by R200,000+',
      color: '#2196f3'
    },
    compliance: {
      name: 'Compliance Management',
      description: 'Stay compliant with automated regulatory tracking and reporting',
      icon: '📋',
      features: [
        'Regulatory compliance tracking',
        'Automated compliance reporting',
        'Certification management',
        'Audit trail maintenance',
        'Document management system',
        'Compliance calendar & alerts',
        'Government integration'
      ],
      benefits: [
        'Avoid compliance penalties',
        'Reduce audit preparation time by 80%',
        'Maintain certification status',
        'Peace of mind with automated tracking'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Avoid R50,000+ in penalties',
      color: '#9c27b0'
    },
    analytics: {
      name: 'Advanced Analytics',
      description: 'AI-powered insights and predictive analytics for optimal decision-making',
      icon: '📊',
      features: [
        'AI-powered farm insights',
        'Predictive analytics engine',
        'Performance benchmarking',
        'Custom dashboard creation',
        'Advanced reporting suite',
        'Machine learning recommendations',
        'Trend analysis & forecasting'
      ],
      benefits: [
        'Improve decision-making by 40%',
        'Predict issues before they occur',
        'Benchmark against top performers',
        'Optimize all farm operations'
      ],
      requiredPlan: 'Enterprise',
      savings: 'Optimize operations worth R300,000+',
      color: '#673ab7'
    }
  };

  // Check if user has access
  if (!user || !canAccessModule(module)) {
    const config = moduleConfig[module];
    
    if (!config) {
      return (
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Alert severity="error">
            <Typography variant="h6">Module Not Found</Typography>
            <Typography>The requested module could not be found.</Typography>
          </Alert>
        </Container>
      );
    }

    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: `linear-gradient(135deg, ${alpha(config.color, 0.1)}, ${alpha(theme.palette.background.default, 0.9)})`,
          py: 8
        }}
      >
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 6 }}>
              <Typography
                variant="h1"
                sx={{
                  fontSize: '4rem',
                  mb: 2
                }}
              >
                {config.icon}
              </Typography>
              
              <Typography
                variant="h3"
                fontWeight="bold"
                sx={{
                  mb: 2,
                  background: `linear-gradient(45deg, ${config.color}, ${theme.palette.primary.main})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                {config.name}
              </Typography>
              
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{ mb: 3, maxWidth: 600, mx: 'auto' }}
              >
                {config.description}
              </Typography>

              <Chip
                label={`Requires ${config.requiredPlan} Plan`}
                color="primary"
                size="large"
                sx={{ fontSize: '1rem', py: 3 }}
              />
            </Box>

            {/* Benefits Alert */}
            <Alert 
              severity="success" 
              sx={{ 
                mb: 4, 
                fontSize: '1.1rem',
                '& .MuiAlert-message': { width: '100%' }
              }}
            >
              <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                {config.savings}
              </Typography>
              <Typography variant="body1">
                Join thousands of farmers who've transformed their operations with {config.name}
              </Typography>
            </Alert>

            <Grid container spacing={4}>
              {/* Features */}
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%', borderRadius: 4 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                      What's Included
                    </Typography>
                    
                    {config.features.map((feature, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 2
                        }}
                      >
                        <CheckCircle sx={{ color: config.color, fontSize: 24 }} />
                        <Typography variant="body1">{feature}</Typography>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>

              {/* Benefits */}
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%', borderRadius: 4 }}>
                  <CardContent sx={{ p: 4 }}>
                    <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
                      Your Benefits
                    </Typography>
                    
                    {config.benefits.map((benefit, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 2,
                          mb: 2
                        }}
                      >
                        <Star sx={{ color: '#ffd700', fontSize: 24 }} />
                        <Typography variant="body1">{benefit}</Typography>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Action Buttons */}
            <Box sx={{ textAlign: 'center', mt: 6 }}>
              <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<Upgrade />}
                    onClick={() => navigate('/pricing')}
                    sx={{
                      px: 4,
                      py: 2,
                      fontSize: '1.2rem',
                      borderRadius: 3,
                      background: `linear-gradient(135deg, ${config.color}, ${theme.palette.primary.main})`,
                      boxShadow: `0 8px 32px ${alpha(config.color, 0.3)}`
                    }}
                  >
                    Upgrade to {config.requiredPlan}
                  </Button>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outlined"
                    size="large"
                    startIcon={<ArrowBack />}
                    onClick={() => navigate('/dashboard')}
                    sx={{
                      px: 4,
                      py: 2,
                      fontSize: '1.2rem',
                      borderRadius: 3
                    }}
                  >
                    Back to Dashboard
                  </Button>
                </motion.div>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mt: 3 }}>
                14-day free trial • No credit card required • Cancel anytime
              </Typography>
            </Box>
          </motion.div>
        </Container>
      </Box>
    );
  }

  // User has access, render the module
  return <>{children}</>;
};

export default ModuleGuard;
