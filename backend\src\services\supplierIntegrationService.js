/**
 * Supplier Integration Service
 * 
 * This service handles fetching and processing data from external supplier websites.
 * It uses a combination of API calls (where available) and web scraping (where necessary)
 * to gather up-to-date information about agricultural suppliers.
 */

const axios = require('axios');
const cheerio = require('cheerio');
const mongodb = require('../config/mongodb');
const logger = require('../utils/logger');

/**
 * Main function to fetch and update supplier data
 */
async function updateSupplierData() {
  try {
    logger.info('Starting supplier data update process');
    
    // Process each supplier
    await Promise.all([
      processAfgriData(),
      processSenwesData(),
      processKaapAgriData(),
      processObaroData(),
      // Add more suppliers as needed
    ]);
    
    logger.info('Supplier data update completed successfully');
    return { success: true, message: 'Supplier data updated successfully' };
  } catch (error) {
    logger.error('Error updating supplier data:', error);
    return { success: false, message: 'Error updating supplier data', error: error.message };
  }
}

/**
 * Process AFGRI data
 */
async function processAfgriData() {
  try {
    logger.info('Fetching AFGRI data');
    
    // Fetch main website data
    const response = await axios.get('https://www.afgri.co.za/');
    const $ = cheerio.load(response.data);
    
    // Extract basic information
    const description = $('div.elementor-widget-container p').first().text().trim();
    
    // Extract contact information
    const contacts = [];
    $('div.elementor-widget-wrap a[href^="tel:"]').each((i, el) => {
      contacts.push({
        type: 'phone',
        value: $(el).text().trim(),
        label: 'Contact'
      });
    });
    
    $('div.elementor-widget-wrap a[href^="mailto:"]').each((i, el) => {
      contacts.push({
        type: 'email',
        value: $(el).text().trim() || $(el).attr('href').replace('mailto:', ''),
        label: 'Email'
      });
    });
    
    // Add website contact
    contacts.push({
      type: 'website',
      value: 'https://www.afgri.co.za',
      label: 'Website'
    });
    
    // Extract services
    const services = [];
    $('div.elementor-widget-wrap h3').each((i, el) => {
      const service = $(el).text().trim();
      if (service) services.push(service);
    });
    
    // Extract featured products (from animal feeds section)
    let featuredProducts = [];
    try {
      const productsResponse = await axios.get('https://www.afgrianimalfeeds.co.za/');
      const $products = cheerio.load(productsResponse.data);
      
      $products('div.product').each((i, el) => {
        if (i < 5) { // Limit to 5 products
          const name = $products(el).find('h2.woocommerce-loop-product__title').text().trim();
          const price = $products(el).find('span.price').text().trim();
          const imageUrl = $products(el).find('img').attr('src');
          
          if (name) {
            featuredProducts.push({
              id: `afgri-prod-${i + 1}`,
              name,
              category: 'livestock-feed',
              description: `AFGRI Animal Feeds - ${name}`,
              imageUrl,
              price: price ? parseFloat(price.replace(/[^0-9.]/g, '')) : null,
              currency: 'ZAR',
              availability: 'in-stock'
            });
          }
        }
      });
    } catch (error) {
      logger.error('Error fetching AFGRI product data:', error);
    }
    
    // Create or update supplier in database
    const supplierData = {
      id: 'afgri-001',
      name: 'AFGRI',
      logo: 'https://www.afgri.co.za/wp-content/uploads/2013/04/AFGRI-Logo.jpg',
      description: description || 'AFGRI is a leading agricultural services company with a core focus on grain commodities and a vision of driving food security across Africa.',
      shortDescription: 'Leading agricultural services company with focus on grain commodities',
      categories: ['livestock-feed', 'equipment', 'financial', 'retail', 'fertilizer', 'chemicals'],
      contacts,
      locations: [
        {
          id: 'afgri-loc-001',
          name: 'AFGRI Head Office',
          address: '12 Byls Bridge Boulevard, Highveld Ext 73, Centurion, 0157',
          coordinates: {
            latitude: -25.8418,
            longitude: 28.1707
          },
          phone: '+27 12 381 2800',
          email: '<EMAIL>'
        }
      ],
      website: 'https://www.afgri.co.za',
      featuredProducts,
      services: services.length > 0 ? services : [
        'Grain Management',
        'Equipment Sales & Service',
        'Animal Feeds',
        'Financial Solutions',
        'Retail Services'
      ],
      established: 1923,
      coverImage: 'https://www.afgri.co.za/wp-content/uploads/2023/05/AFGRI-Web-Button-Logo-About-Us.png',
      rating: 4.8,
      lastUpdated: new Date()
    };
    
    const db = await mongodb.getDb();
    await db.collection('suppliers').replaceOne(
      { id: supplierData.id },
      supplierData,
      { upsert: true }
    );

    // Save products separately if needed
    if (featuredProducts.length > 0) {
      for (const product of featuredProducts) {
        await db.collection('supplier_products').replaceOne(
          { id: product.id },
          { ...product, supplierId: supplierData.id },
          { upsert: true }
        );
      }
    }
    
    logger.info('AFGRI data updated successfully');
    return true;
  } catch (error) {
    logger.error('Error processing AFGRI data:', error);
    return false;
  }
}

/**
 * Process Senwes data
 */
async function processSenwesData() {
  try {
    logger.info('Fetching Senwes data');
    
    // Fetch main website data
    const response = await axios.get('https://www.senwes.co.za/');
    const $ = cheerio.load(response.data);
    
    // Extract basic information
    const description = $('div.resilient-drive-towards-excellence').text().trim();
    
    // Extract contact information
    const contacts = [];
    
    // Add known contacts
    contacts.push({
      type: 'phone',
      value: '+27 18 464 7800',
      label: 'Head Office'
    });
    
    contacts.push({
      type: 'email',
      value: '<EMAIL>',
      label: 'General Inquiries'
    });
    
    contacts.push({
      type: 'website',
      value: 'https://www.senwes.co.za',
      label: 'Website'
    });
    
    contacts.push({
      type: 'address',
      value: '1 Charel de Klerk Street, Klerksdorp, 2571',
      label: 'Head Office'
    });
    
    // Extract services
    const services = [];
    $('div.products-services a').each((i, el) => {
      const service = $(el).text().trim();
      if (service) services.push(service);
    });
    
    // Create or update supplier in database
    const supplierData = {
      id: 'senwes-001',
      name: 'Senwes',
      logo: 'https://www.senwes.co.za/media/Global/images/Senwes/MainLogoGreen_Transparent.png',
      description: description || 'Senwes is one of the leading agricultural companies in South Africa, providing integrated solutions to agricultural producers.',
      shortDescription: 'Leading agricultural company providing integrated solutions',
      categories: ['livestock-feed', 'equipment', 'financial', 'retail', 'fertilizer', 'chemicals', 'insurance'],
      contacts,
      locations: [
        {
          id: 'senwes-loc-001',
          name: 'Senwes Head Office',
          address: '1 Charel de Klerk Street, Klerksdorp, 2571',
          coordinates: {
            latitude: -26.8711,
            longitude: 26.6658
          },
          phone: '+27 18 464 7800',
          email: '<EMAIL>'
        }
      ],
      website: 'https://www.senwes.co.za',
      services: services.length > 0 ? services : [
        'Grain Storage & Handling',
        'Equipment Sales & Service',
        'Financing',
        'Insurance',
        'Retail Services',
        'Agricultural Economics'
      ],
      established: 1909,
      coverImage: 'https://media.senwes.co.za/global/web/assets/img/clients/Silo_sunflower_dark-clouds.jpg',
      rating: 4.6,
      lastUpdated: new Date()
    };
    
    const db = await mongodb.getDb();
    await db.collection('suppliers').replaceOne(
      { id: supplierData.id },
      supplierData,
      { upsert: true }
    );
    
    logger.info('Senwes data updated successfully');
    return true;
  } catch (error) {
    logger.error('Error processing Senwes data:', error);
    return false;
  }
}

/**
 * Process KAL Group (formerly Kaap Agri) data
 */
async function processKaapAgriData() {
  try {
    logger.info('Fetching KAL Group data');
    
    // Fetch main website data
    const response = await axios.get('https://www.kalgroup.co.za/');
    const $ = cheerio.load(response.data);
    
    // Extract basic information
    const description = $('div.we-are-the-place-where-people-still-count').text().trim();
    
    // Extract contact information
    const contacts = [];
    
    // Add known contacts
    contacts.push({
      type: 'phone',
      value: '+27 21 860 3750',
      label: 'Head Office'
    });
    
    contacts.push({
      type: 'email',
      value: '<EMAIL>',
      label: 'General Inquiries'
    });
    
    contacts.push({
      type: 'website',
      value: 'https://www.kalgroup.co.za',
      label: 'Website'
    });
    
    contacts.push({
      type: 'address',
      value: '1 Westhoven Street, Paarl, 7646',
      label: 'Head Office'
    });
    
    // Extract divisions/services
    const services = [];
    $('div.divisions a').each((i, el) => {
      const service = $(el).text().trim();
      if (service) services.push(service);
    });
    
    // Create or update supplier in database
    const supplierData = {
      id: 'kaap-agri-001',
      name: 'KAL Group (formerly Kaap Agri)',
      logo: 'https://www.kalgroup.co.za/modules/kaap-agri-theme/images/kal-group-logo.svg',
      description: description || 'KAL Group is a diversified JSE-listed company that provides a range of products and services to the agricultural and retail-related markets of Southern Africa.',
      shortDescription: 'Diversified agricultural and retail services company',
      categories: ['retail', 'equipment', 'fertilizer', 'chemicals', 'irrigation', 'fuel'],
      contacts,
      locations: [
        {
          id: 'kaap-agri-loc-001',
          name: 'KAL Group Head Office',
          address: '1 Westhoven Street, Paarl, 7646',
          coordinates: {
            latitude: -33.7143,
            longitude: 18.9744
          },
          phone: '+27 21 860 3750',
          email: '<EMAIL>'
        }
      ],
      website: 'https://www.kalgroup.co.za',
      services: services.length > 0 ? services : [
        'Agrimark',
        'Tego Plastics',
        'PEG Retail Operations',
        'Agriplas',
        'Expressmark',
        'FarmSave'
      ],
      established: 1912,
      coverImage: 'https://www.kalgroup.co.za/modules/kaap-agri-theme/images/svg/apple-store.svg',
      rating: 4.2,
      lastUpdated: new Date()
    };
    
    const db = await mongodb.getDb();
    await db.collection('suppliers').replaceOne(
      { id: supplierData.id },
      supplierData,
      { upsert: true }
    );
    
    logger.info('KAL Group data updated successfully');
    return true;
  } catch (error) {
    logger.error('Error processing KAL Group data:', error);
    return false;
  }
}

/**
 * Process OBARO data
 */
async function processObaroData() {
  try {
    logger.info('Fetching OBARO data');
    
    // Fetch main website data
    const response = await axios.get('https://www.obaro.co.za/');
    const $ = cheerio.load(response.data);
    
    // Extract basic information
    const description = $('div.elementor-widget-container p').first().text().trim();
    
    // Extract contact information
    const contacts = [];
    
    // Add known contacts
    contacts.push({
      type: 'phone',
      value: '+27 12 381 2800',
      label: 'Head Office'
    });
    
    contacts.push({
      type: 'email',
      value: '<EMAIL>',
      label: 'General Inquiries'
    });
    
    contacts.push({
      type: 'website',
      value: 'https://www.obaro.co.za',
      label: 'Website'
    });
    
    contacts.push({
      type: 'address',
      value: 'C/o Graham & Silverlakes Road, Silverlakes, Pretoria, 0081',
      label: 'Head Office'
    });
    
    // Extract services
    const services = [];
    $('div.finance, div.insurance, div.advisory-services, div.irrigation-design, div.mechanisation, div.animal-production, div.agriculture-chemicals, div.packaging').each((i, el) => {
      const service = $(el).find('h3').text().trim();
      if (service) services.push(service);
    });
    
    // Create or update supplier in database
    const supplierData = {
      id: 'obaro-001',
      name: 'OBARO',
      logo: 'https://obaro.co.za/wp-content/uploads/2023/01/Logo-copy-02.svg',
      description: description || 'OBARO is a leading agricultural company in South Africa with a mission to be aligned with the farmer.',
      shortDescription: 'Agricultural company aligned with the farmer',
      categories: ['retail', 'equipment', 'fertilizer', 'chemicals', 'irrigation', 'fuel', 'financial', 'insurance', 'animal-health'],
      contacts,
      locations: [
        {
          id: 'obaro-loc-001',
          name: 'OBARO Head Office',
          address: 'C/o Graham & Silverlakes Road, Silverlakes, Pretoria, 0081',
          coordinates: {
            latitude: -25.7868,
            longitude: 28.3815
          },
          phone: '+27 12 381 2800',
          email: '<EMAIL>'
        }
      ],
      website: 'https://www.obaro.co.za',
      services: services.length > 0 ? services : [
        'Finance',
        'Insurance',
        'Advisory Services',
        'Irrigation Design',
        'Mechanisation',
        'Animal Production',
        'Agriculture Chemicals',
        'Packaging'
      ],
      established: 1930,
      coverImage: 'https://obaro.co.za/wp-content/uploads/2023/01/image-1.svg',
      rating: 4.5,
      lastUpdated: new Date()
    };
    
    const db = await mongodb.getDb();
    await db.collection('suppliers').replaceOne(
      { id: supplierData.id },
      supplierData,
      { upsert: true }
    );
    
    logger.info('OBARO data updated successfully');
    return true;
  } catch (error) {
    logger.error('Error processing OBARO data:', error);
    return false;
  }
}

/**
 * Schedule regular updates
 * @param {number} intervalHours - How often to update (in hours)
 */
function scheduleUpdates(intervalHours = 24) {
  const intervalMs = intervalHours * 60 * 60 * 1000;
  
  // Initial update
  updateSupplierData();
  
  // Schedule regular updates
  setInterval(updateSupplierData, intervalMs);
  
  logger.info(`Scheduled supplier data updates every ${intervalHours} hours`);
}

module.exports = {
  updateSupplierData,
  scheduleUpdates
};
