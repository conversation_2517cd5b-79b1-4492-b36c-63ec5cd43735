/**
 * Inventory Management API Routes
 *
 * This module provides API routes for inventory management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');
const inventoryController = require('../../controllers/mongodb/inventoryController');

/**
 * @route GET /api/inventory
 * @desc Get inventory overview
 * @access Private
 */
router.get('/', inventoryController.getInventory);

/**
 * @route GET /api/inventory/items
 * @desc Get all inventory items
 * @access Private
 */
router.get('/items', authenticate, inventoryController.getInventoryItems);

/**
 * @route GET /api/inventory/items/:id
 * @desc Get inventory item by ID
 * @access Private
 */
router.get('/items/:id', authenticate, inventoryController.getInventoryItemById);

/**
 * @route POST /api/inventory/items
 * @desc Create a new inventory item
 * @access Private
 */
router.post('/items', authenticate, inventoryController.createInventoryItem);

/**
 * @route PUT /api/inventory/items/:id
 * @desc Update an inventory item
 * @access Private
 */
router.put('/items/:id', authenticate, inventoryController.updateInventoryItem);

/**
 * @route DELETE /api/inventory/items/:id
 * @desc Delete an inventory item
 * @access Private
 */
router.delete('/items/:id', authenticate, inventoryController.deleteInventoryItem);

/**
 * @route GET /api/inventory/stats
 * @desc Get inventory statistics
 * @access Private
 */
router.get('/stats', authenticate, inventoryController.getInventoryStatistics);



module.exports = router;
