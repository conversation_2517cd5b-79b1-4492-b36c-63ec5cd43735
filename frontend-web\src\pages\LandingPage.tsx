import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  AppBar,
  Toolbar,
  IconButton,
  Dialog,
  DialogContent,
  Chip,
  Avatar,
  useTheme,
  alpha,
  Fade,
  Slide,
  Zoom,
  Paper,
  Backdrop
} from '@mui/material';
import '../styles/modern-interactive.css';
import '../styles/landing-page.css';
import {
  Agriculture,
  TrendingUp,
  Security,
  CloudSync,
  Analytics,
  Pets,
  LocalHospital,
  AccountBalance,
  Inventory,
  Assessment,
  Settings,
  Star,
  CheckCircle,
  ArrowForward,
  PlayArrow,
  Language,
  Login as LoginIcon,
  PersonAdd,
  AutoAwesome,
  Nature as Eco,
  Psychology,
  Speed,
  Shield,
  Public,
  SupportAgent as Support
} from '@mui/icons-material';
import { motion, AnimatePresence, useScroll, useTransform, useSpring } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import LanguageSelector from '../components/LanguageSelector';
import { useTranslation } from '../hooks/useTranslation';
import { moduleBackgrounds } from '../utils/localImages';

// Enhanced Life & Nature Design System
const lifeNatureDesign = {
  colors: {
    // Primary Green Life Palette
    primary: '#2E7D32', // Deep Forest Green
    primaryLight: '#4CAF50', // Life Green
    primaryDark: '#1B5E20', // Dark Forest

    // Secondary Nature Palette
    secondary: '#388E3C', // Nature Green
    secondaryLight: '#66BB6A', // Fresh Green
    secondaryDark: '#2E7D32', // Deep Nature

    // Accent Colors
    accent: '#81C784', // Soft Life Green
    accentBright: '#A5D6A7', // Bright Nature
    accentGold: '#FFC107', // Golden Harvest

    // Surface & Background
    surface: 'rgba(76, 175, 80, 0.15)', // Translucent Green
    surfaceLight: 'rgba(129, 199, 132, 0.1)', // Light Green Surface
    background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)',

    // Text Colors
    text: '#FFFFFF',
    textSecondary: 'rgba(255, 255, 255, 0.9)',
    textAccent: '#E8F5E8',

    // Gradients
    gradient: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    accentGradient: 'linear-gradient(135deg, #4CAF50 0%, #81C784 100%)',
    cardGradient: 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.1) 100%)'
  },
  gradients: {
    primary: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
    secondary: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 100%)',
    accent: 'linear-gradient(135deg, #81C784 0%, #A5D6A7 100%)',
    life: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 30%, #4CAF50 60%, #81C784 100%)',
    nature: 'linear-gradient(45deg, #2E7D32 0%, #388E3C 25%, #4CAF50 50%, #66BB6A 75%, #81C784 100%)',
    harvest: 'linear-gradient(135deg, #4CAF50 0%, #FFC107 50%, #FF8F00 100%)',
    pageBackground: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #388E3C 50%, #4CAF50 75%, #66BB6A 100%)'
  },
  shapes: {
    borderRadius: {
      small: '16px',
      medium: '24px',
      large: '36px',
      xl: '48px',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%'
    },
    clipPath: {
      hexagon: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
      diamond: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
      leaf: 'polygon(50% 0%, 80% 10%, 100% 35%, 85% 70%, 50% 100%, 15% 70%, 0% 35%, 20% 10%)',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%',
      circle: '50%',
      pentagon: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)'
    }
  }
};

// Premium Colors for compatibility
const premiumColors = {
  emeraldGreen: {
    primary: '#1B5E20',
    secondary: '#2E7D32',
    accent: '#4CAF50',
    light: '#81C784'
  },
  deepBlue: {
    primary: '#0D47A1',
    secondary: '#1565C0',
    accent: '#1976D2',
    light: '#42A5F5'
  },
  warmGold: {
    primary: '#E65100',
    secondary: '#F57C00',
    accent: '#FF9800',
    light: '#FFB74D'
  }
};

const modernDesign = {
  colors: {
    // Map lifeNatureDesign colors to modernDesign structure
    primary: lifeNatureDesign.colors.primary,
    secondary: lifeNatureDesign.colors.secondary,
    accent: lifeNatureDesign.colors.accent,
    accentSecondary: lifeNatureDesign.colors.accentBright,
    surface: lifeNatureDesign.colors.surface,
    surfaceHover: lifeNatureDesign.colors.surfaceLight,
    text: lifeNatureDesign.colors.text,
    textSecondary: lifeNatureDesign.colors.textSecondary,
    gradient: lifeNatureDesign.colors.gradient,
    accentGradient: lifeNatureDesign.colors.accentGradient,
    cardGradient: lifeNatureDesign.colors.cardGradient
  },
  shapes: {
    borderRadius: {
      small: '16px',
      medium: '24px',
      large: '36px',
      xl: '48px',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%'
    },
    clipPath: {
      hexagon: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
      diamond: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
      leaf: 'polygon(50% 0%, 80% 10%, 100% 35%, 85% 70%, 50% 100%, 15% 70%, 0% 35%, 20% 10%)',
      organic: '30% 70% 70% 30% / 30% 30% 70% 70%',
      circle: '50%',
      pentagon: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)'
    }
  },
  animations: {
    float: {
      y: [-10, 10, -10],
      transition: { duration: 6, repeat: Infinity, ease: "easeInOut" }
    },
    pulse: {
      scale: [1, 1.05, 1],
      transition: { duration: 2, repeat: Infinity, ease: "easeInOut" }
    },
    rotate: {
      rotate: [0, 360],
      transition: { duration: 20, repeat: Infinity, ease: "linear" }
    }
  }
};

// Subtle background with minimal effects
const SubtleBackground: React.FC = () => {
  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: modernDesign.colors.gradient,
        zIndex: 0
      }}
    >
      {/* Subtle floating shapes - much fewer and less intrusive */}
      {Array.from({ length: 5 }).map((_, i) => (
        <motion.div
          key={i}
          style={{
            position: 'absolute',
            width: 60 + Math.random() * 40,
            height: 60 + Math.random() * 40,
            background: `linear-gradient(45deg, ${alpha(modernDesign.colors.accent, 0.03)}, ${alpha(modernDesign.colors.accentSecondary, 0.03)})`,
            borderRadius: '50%',
            left: `${20 + Math.random() * 60}%`,
            top: `${20 + Math.random() * 60}%`,
          }}
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.1, 0.2, 0.1],
            x: [0, Math.random() * 30 - 15, 0],
            y: [0, Math.random() * 30 - 15, 0],
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        />
      ))}
    </Box>
  );
};

// Enhanced modern card components with octagonal/hexagonal boards
interface ModernCardProps {
  children: React.ReactNode;
  shape?: 'octagon' | 'hexagon' | 'diamond' | 'pentagon' | 'rounded';
  variant?: 'glass' | 'solid' | 'gradient' | 'neon' | 'board';
  size?: 'small' | 'medium' | 'large';
  animate?: boolean;
  backgroundImage?: string;
  sx?: any;
}

const ModernCard: React.FC<ModernCardProps> = ({
  children,
  shape = 'rounded',
  variant = 'glass',
  size = 'medium',
  animate = true,
  backgroundImage,
  sx = {}
}) => {
  const getCardStyles = () => {
    const baseStyles = {
      position: 'relative',
      overflow: 'hidden',
      transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    };

    const sizeStyles = {
      small: { minHeight: '220px' },
      medium: { minHeight: '300px' },
      large: { minHeight: '380px' }
    };

    const shapeStyles = {
      octagon: {
        clipPath: 'polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)',
        borderRadius: 0
      },
      hexagon: {
        clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
        borderRadius: 0
      },
      diamond: {
        clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
        borderRadius: 0,
        transform: 'rotate(0deg)'
      },
      pentagon: {
        clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)',
        borderRadius: 0
      },
      rounded: {
        borderRadius: modernDesign.shapes.borderRadius.large
      }
    };

    const variantStyles = {
      glass: {
        background: modernDesign.colors.surface,
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(modernDesign.colors.accent, 0.2)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.3)}`
      },
      solid: {
        background: modernDesign.colors.secondary,
        border: `1px solid ${alpha(modernDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha('#000', 0.4)}`
      },
      gradient: {
        background: modernDesign.colors.cardGradient,
        border: `1px solid ${alpha(modernDesign.colors.accent, 0.3)}`,
        boxShadow: `0 8px 32px ${alpha(modernDesign.colors.accent, 0.2)}`
      },
      neon: {
        background: modernDesign.colors.surface,
        border: `2px solid ${modernDesign.colors.accent}`,
        boxShadow: `0 0 20px ${alpha(modernDesign.colors.accent, 0.5)}, inset 0 0 20px ${alpha(modernDesign.colors.accent, 0.1)}`
      },
      board: {
        background: `linear-gradient(135deg, ${modernDesign.colors.surface}, ${alpha(modernDesign.colors.accent, 0.1)})`,
        border: `2px solid ${alpha(modernDesign.colors.accent, 0.4)}`,
        boxShadow: `0 12px 40px ${alpha('#000', 0.4)}, inset 0 2px 4px ${alpha(modernDesign.colors.accent, 0.2)}`
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...shapeStyles[shape],
      ...variantStyles[variant],
      ...sx
    };
  };

  const cardContent = (
    <Box sx={getCardStyles()}>
      {/* Background image with blend */}
      {backgroundImage && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.4,
            filter: 'brightness(0.7) contrast(1.1)',
            zIndex: 0,
            clipPath: shape !== 'rounded' ? getCardStyles().clipPath : 'none',
            borderRadius: shape === 'rounded' ? modernDesign.shapes.borderRadius.large : 0
          }}
        />
      )}

      {/* Animated gradient overlay */}
      <motion.div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at 30% 30%, ${alpha(modernDesign.colors.accent, 0.1)} 0%, transparent 70%)`,
          zIndex: 1
        }}
        animate={{
          background: [
            `radial-gradient(circle at 30% 30%, ${alpha(modernDesign.colors.accent, 0.1)} 0%, transparent 70%)`,
            `radial-gradient(circle at 70% 70%, ${alpha(modernDesign.colors.accentSecondary, 0.1)} 0%, transparent 70%)`,
            `radial-gradient(circle at 30% 30%, ${alpha(modernDesign.colors.accent, 0.1)} 0%, transparent 70%)`
          ]
        }}
        transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
      />

      {/* Content */}
      <Box sx={{ position: 'relative', zIndex: 2, height: '100%', p: 3 }}>
        {children}
      </Box>

      {/* Hover effect overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(45deg, ${alpha(modernDesign.colors.accent, 0.15)}, ${alpha(modernDesign.colors.accentSecondary, 0.15)})`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
          zIndex: 1,
          '.parent:hover &': {
            opacity: 1
          }
        }}
      />
    </Box>
  );

  if (!animate) return cardContent;

  return (
    <motion.div
      className="parent"
      whileHover={{
        scale: 1.03,
        y: -8,
        rotateY: 2,
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.97 }}
      initial={{ opacity: 0, y: 30, rotateX: -10 }}
      whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
    >
      {cardContent}
    </motion.div>
  );
};

// Modern section divider with animated shapes
const SectionDivider: React.FC<{ variant?: 'wave' | 'zigzag' | 'curve' | 'geometric' }> = ({
  variant = 'wave'
}) => {
  const getDividerPath = () => {
    switch (variant) {
      case 'wave':
        return 'M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,96C960,107,1056,117,1152,112C1248,107,1344,85,1392,74.7L1440,64L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z';
      case 'zigzag':
        return 'M0,0L60,32L120,0L180,32L240,0L300,32L360,0L420,32L480,0L540,32L600,0L660,32L720,0L780,32L840,0L900,32L960,0L1020,32L1080,0L1140,32L1200,0L1260,32L1320,0L1380,32L1440,0V64H0V0Z';
      case 'curve':
        return 'M0,32L1440,96L1440,0L0,0Z';
      case 'geometric':
        return 'M0,0L720,64L1440,0V64H0V0Z';
      default:
        return 'M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,85.3C672,75,768,85,864,96C960,107,1056,117,1152,112C1248,107,1344,85,1392,74.7L1440,64L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z';
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        height: '100px',
        overflow: 'hidden',
        transform: 'translateY(-1px)'
      }}
    >
      <motion.svg
        viewBox="0 0 1440 64"
        style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          height: '100%',
        }}
        animate={{
          x: [0, -100, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      >
        <defs>
          <linearGradient id="dividerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor={modernDesign.colors.accent} stopOpacity="0.8" />
            <stop offset="50%" stopColor={modernDesign.colors.accentSecondary} stopOpacity="0.6" />
            <stop offset="100%" stopColor={modernDesign.colors.accent} stopOpacity="0.8" />
          </linearGradient>
        </defs>
        <path
          d={getDividerPath()}
          fill="url(#dividerGradient)"
        />
      </motion.svg>
    </Box>
  );
};

const LandingPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showVideo, setShowVideo] = useState(false);
  const [hoveredFeature, setHoveredFeature] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Scroll-based animations
  const { scrollYProgress } = useScroll();
  const yTransform = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const scaleTransform = useTransform(scrollYProgress, [0, 0.5], [1, 1.1]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.3], [1, 0.8]);

  // Smooth spring animations
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  const x = useSpring(0, springConfig);
  const y = useSpring(0, springConfig);

  // Mouse tracking for parallax effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const { innerWidth, innerHeight } = window;
      setMousePosition({
        x: (clientX / innerWidth - 0.5) * 2,
        y: (clientY / innerHeight - 0.5) * 2
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Auto-rotate hero slides
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % 4);
    }, 6000);
    return () => clearInterval(timer);
  }, []);

  const heroSlides = [
    {
      titleKey: "landing.hero.slide1.title",
      subtitleKey: "landing.hero.slide1.subtitle",
      image: 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
      gradient: 'linear-gradient(135deg, #1E40AF 0%, #10B981 100%)',
      overlay: 'rgba(15, 23, 42, 0.7)'
    },
    {
      titleKey: "landing.hero.slide2.title",
      subtitleKey: "landing.hero.slide2.subtitle",
      image: 'https://images.unsplash.com/photo-1560493676-04071c5f467b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
      gradient: 'linear-gradient(135deg, #10B981 0%, #F59E0B 100%)',
      overlay: 'rgba(6, 78, 59, 0.7)'
    },
    {
      titleKey: "landing.hero.slide3.title",
      subtitleKey: "landing.hero.slide3.subtitle",
      image: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2074&q=80',
      gradient: 'linear-gradient(135deg, #F59E0B 0%, #1E40AF 100%)',
      overlay: 'rgba(146, 64, 14, 0.7)'
    },
    {
      titleKey: "landing.hero.slide4.title",
      subtitleKey: "landing.hero.slide4.subtitle",
      image: 'https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=2071&q=80',
      gradient: `linear-gradient(135deg, ${premiumColors.deepBlue.secondary} 0%, ${premiumColors.emeraldGreen.secondary} 100%)`,
      overlay: 'rgba(30, 41, 59, 0.7)'
    }
  ];

  const features = [
    {
      icon: <Pets />,
      titleKey: "landing.features.animals.title",
      descriptionKey: "landing.features.animals.description",
      color: '#10B981',
      gradient: 'linear-gradient(135deg, #10B981, #34D399)',
      image: 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    },
    {
      icon: <LocalHospital />,
      titleKey: "landing.features.health.title",
      descriptionKey: "landing.features.health.description",
      color: '#3B82F6',
      gradient: 'linear-gradient(135deg, #3B82F6, #60A5FA)',
      image: 'https://images.unsplash.com/photo-*************-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    },
    {
      icon: <Psychology />,
      titleKey: "landing.features.analytics.title",
      descriptionKey: "landing.features.analytics.description",
      color: '#F59E0B',
      gradient: 'linear-gradient(135deg, #F59E0B, #FBBF24)',
      image: '/images/modules/rfid/rfid-1.webp'
    },
    {
      icon: <AccountBalance />,
      titleKey: "landing.features.financial.title",
      descriptionKey: "landing.features.financial.description",
      color: '#8B5CF6',
      gradient: 'linear-gradient(135deg, #8B5CF6, #A78BFA)',
      image: '/images/modules/animals/cattle-2.avif'
    },
    {
      icon: <Inventory />,
      titleKey: "landing.features.inventory.title",
      descriptionKey: "landing.features.inventory.description",
      color: '#EF4444',
      gradient: 'linear-gradient(135deg, #EF4444, #F87171)',
      image: '/images/modules/feeding/feed-main.jpeg'
    },
    {
      icon: <Assessment />,
      titleKey: "landing.features.reports.title",
      descriptionKey: "landing.features.reports.description",
      color: '#6B7280',
      gradient: 'linear-gradient(135deg, #6B7280, #9CA3AF)',
      image: '/images/modules/health/veterinary-1.jpg'
    },
    {
      icon: <Shield />,
      titleKey: "landing.features.security.title",
      descriptionKey: "landing.features.security.description",
      color: '#059669',
      gradient: 'linear-gradient(135deg, #059669, #10B981)',
      image: '/images/modules/rfid/rfid-2.jpg'
    },
    {
      icon: <Speed />,
      titleKey: "landing.features.performance.title",
      descriptionKey: "landing.features.performance.description",
      color: '#DC2626',
      gradient: 'linear-gradient(135deg, #DC2626, #EF4444)',
      image: '/images/modules/animals/cattle-3.jpeg'
    },
    {
      icon: <Public />,
      titleKey: "landing.features.global.title",
      descriptionKey: "landing.features.global.description",
      color: '#7C3AED',
      gradient: 'linear-gradient(135deg, #7C3AED, #8B5CF6)',
      image: '/images/modules/health/veterinary-2.jpg'
    }
  ];

  // Enhanced subscription tiers with South African pricing
  const subscriptionTiers = [
    {
      nameKey: "landing.pricing.beta.name",
      priceKey: "landing.pricing.beta.price",
      durationKey: "landing.pricing.beta.duration",
      descriptionKey: "landing.pricing.beta.description",
      featuresKeys: [
        "landing.pricing.beta.feature1",
        "landing.pricing.beta.feature2",
        "landing.pricing.beta.feature3",
        "landing.pricing.beta.feature4"
      ],
      color: premiumColors.emeraldGreen.accent,
      gradient: `linear-gradient(135deg, ${premiumColors.emeraldGreen.accent}, ${premiumColors.emeraldGreen.light})`,
      popular: false,
      actionKey: "landing.pricing.beta.action",
      icon: <Eco />,
      price: "Free",
      originalPrice: null,
      savings: null,
      badge: "Beta Access",
      animalLimit: "50 animals",
      features: [
        "Basic Animal Management",
        "Health Record Tracking",
        "Feed Management",
        "Basic Reports",
        "Community Support"
      ]
    },
    {
      nameKey: "landing.pricing.professional.name",
      priceKey: "landing.pricing.professional.price",
      durationKey: "landing.pricing.professional.duration",
      descriptionKey: "landing.pricing.professional.description",
      featuresKeys: [
        "landing.pricing.professional.feature1",
        "landing.pricing.professional.feature2",
        "landing.pricing.professional.feature3",
        "landing.pricing.professional.feature4",
        "landing.pricing.professional.feature5",
        "landing.pricing.professional.feature6"
      ],
      color: premiumColors.deepBlue.accent,
      gradient: `linear-gradient(135deg, ${premiumColors.deepBlue.accent}, #60A5FA)`,
      popular: true,
      actionKey: "landing.pricing.professional.action",
      icon: <TrendingUp />,
      price: "R299",
      originalPrice: "R399",
      savings: "25%",
      badge: "Most Popular",
      animalLimit: "500 animals",
      features: [
        "Everything in Beta",
        "Advanced Analytics & AI Insights",
        "Breeding Management",
        "Financial Tracking & ROI",
        "Inventory Management",
        "Priority Support",
        "Mobile App Access",
        "Custom Reports"
      ]
    },
    {
      nameKey: "landing.pricing.enterprise.name",
      priceKey: "landing.pricing.enterprise.price",
      durationKey: "landing.pricing.enterprise.duration",
      descriptionKey: "landing.pricing.enterprise.description",
      featuresKeys: [
        "landing.pricing.enterprise.feature1",
        "landing.pricing.enterprise.feature2",
        "landing.pricing.enterprise.feature3",
        "landing.pricing.enterprise.feature4",
        "landing.pricing.enterprise.feature5",
        "landing.pricing.enterprise.feature6"
      ],
      color: premiumColors.warmGold.accent,
      gradient: `linear-gradient(135deg, ${premiumColors.warmGold.accent}, ${premiumColors.warmGold.light})`,
      popular: false,
      actionKey: "landing.pricing.enterprise.action",
      icon: <AutoAwesome />,
      price: "R599",
      originalPrice: "R799",
      savings: "25%",
      badge: "Enterprise",
      animalLimit: "Unlimited",
      features: [
        "Everything in Professional",
        "Multi-Farm Management",
        "Advanced Compliance Tools",
        "API Access & Integrations",
        "White-label Options",
        "Dedicated Account Manager",
        "Custom Training & Onboarding",
        "24/7 Premium Support"
      ]
    }
  ];

  const stats = [
    { numberKey: "landing.stats.farmers.number", labelKey: "landing.stats.farmers.label", icon: <Agriculture />, number: "5,000+", label: "Active Farmers" },
    { numberKey: "landing.stats.animals.number", labelKey: "landing.stats.animals.label", icon: <Pets />, number: "250K+", label: "Animals Tracked" },
    { numberKey: "landing.stats.satisfaction.number", labelKey: "landing.stats.satisfaction.label", icon: <Star />, number: "98%", label: "Satisfaction Rate" },
    { numberKey: "landing.stats.support.number", labelKey: "landing.stats.support.label", icon: <Support />, number: "24/7", label: "Expert Support" }
  ];

  // Professional testimonials
  const testimonials = [
    {
      name: "Pieter van der Merwe",
      title: "Commercial Cattle Farmer",
      location: "Free State, South Africa",
      image: "/images/testimonials/farmer-1.jpg",
      quote: "AgriIntel has revolutionized how we manage our 2,000 head of cattle. The AI insights have improved our breeding efficiency by 35% and reduced veterinary costs significantly.",
      rating: 5,
      company: "Van der Merwe Cattle Ranch"
    },
    {
      name: "Dr. Sarah Mthembu",
      title: "Veterinarian & Farm Owner",
      location: "KwaZulu-Natal, South Africa",
      image: "/images/testimonials/vet-1.jpg",
      quote: "As both a veterinarian and farmer, I appreciate AgriIntel's comprehensive health tracking. It's like having a digital assistant that never misses a vaccination or treatment.",
      rating: 5,
      company: "Mthembu Veterinary Services"
    },
    {
      name: "Johan Steyn",
      title: "Dairy Farm Manager",
      location: "Western Cape, South Africa",
      image: "/images/testimonials/manager-1.jpg",
      quote: "The financial tracking and ROI analysis features have helped us increase profitability by 28%. AgriIntel pays for itself within the first month.",
      rating: 5,
      company: "Steyn Dairy Farms"
    }
  ];

  // Client logos/partners
  const clientLogos = [
    { name: "AFGRI", logo: "/images/clients/afgri-logo.png", url: "https://www.afgri.co.za" },
    { name: "Senwes", logo: "/images/clients/senwes-logo.png", url: "https://www.senwes.co.za" },
    { name: "KAL Group", logo: "/images/clients/kal-logo.png", url: "https://www.kalgroup.co.za" },
    { name: "OBARO", logo: "/images/clients/obaro-logo.png", url: "https://www.obaro.co.za" },
    { name: "University of Pretoria", logo: "/images/clients/up-logo.png", url: "https://www.up.ac.za" },
    { name: "Stellenbosch University", logo: "/images/clients/sun-logo.png", url: "https://www.sun.ac.za" }
  ];

  return (
    <Box sx={{ position: 'relative', minHeight: '100vh' }}>
      {/* Subtle Background */}
      <SubtleBackground />

      {/* Navigation */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          background: alpha(modernDesign.colors.primary, 0.95),
          backdropFilter: 'blur(30px)',
          borderBottom: `1px solid ${alpha(modernDesign.colors.accent, 0.2)}`,
          zIndex: 1300
        }}
      >
        <Toolbar sx={{ py: 1 }}>
          <motion.div
            style={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                background: modernDesign.colors.accentGradient,
                borderRadius: modernDesign.shapes.borderRadius.medium,
                p: 1.5,
                mr: 2,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: modernDesign.colors.accentGradient,
                  opacity: 0.8,
                  filter: 'blur(8px)',
                  zIndex: -1
                }
              }}
            >
              <Agriculture sx={{ color: 'white', fontSize: 32 }} />
            </Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 900,
                color: modernDesign.colors.text,
                background: modernDesign.colors.accentGradient,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                letterSpacing: '-0.02em'
              }}
            >
              AgriIntel
            </Typography>
          </motion.div>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            {/* Enhanced Language Selector */}
            <Box sx={{ position: 'relative' }}>
              <LanguageSelector />
            </Box>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                startIcon={<LoginIcon />}
                onClick={() => navigate('/login')}
                sx={{
                  color: modernDesign.colors.text,
                  borderRadius: modernDesign.shapes.borderRadius.medium,
                  px: 3,
                  py: 1.5,
                  background: modernDesign.colors.surface,
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(modernDesign.colors.accent, 0.3)}`,
                  '&:hover': {
                    background: modernDesign.colors.surfaceHover,
                    borderColor: modernDesign.colors.accent,
                    boxShadow: `0 4px 20px ${alpha(modernDesign.colors.accent, 0.3)}`
                  }
                }}
              >
                {translate('landing.nav.login', { fallback: 'Login' })}
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="contained"
                startIcon={<PersonAdd />}
                onClick={() => navigate('/register')}
                sx={{
                  background: modernDesign.colors.accentGradient,
                  boxShadow: `0 8px 32px ${alpha(modernDesign.colors.accent, 0.4)}`,
                  borderRadius: modernDesign.shapes.borderRadius.medium,
                  px: 4,
                  py: 1.5,
                  fontWeight: 'bold',
                  fontSize: '1rem',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: modernDesign.colors.accentGradient,
                    opacity: 0,
                    transition: 'opacity 0.3s ease',
                    zIndex: -1
                  },
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: `0 12px 40px ${alpha(modernDesign.colors.accent, 0.6)}`,
                    '&::before': {
                      opacity: 0.2
                    }
                  }
                }}
              >
                {translate('landing.nav.getStarted', { fallback: 'Get Started' })}
              </Button>
            </motion.div>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Box className="hero-section">
        {/* Floating Elements */}
        <div className="floating-elements">
          <div className="floating-particle"></div>
          <div className="floating-particle"></div>
          <div className="floating-particle"></div>
          <div className="floating-particle"></div>
        </div>

        {/* Dynamic Background Image with Parallax */}
        <motion.div
          className="hero-background"
          style={{
            backgroundImage: `url(${heroSlides[currentSlide].image})`,
            scale: scaleTransform,
          }}
          animate={{
            x: mousePosition.x * 5,
            y: mousePosition.y * 5,
          }}
          transition={{ type: "spring", stiffness: 30, damping: 20 }}
        />

        {/* Hero Overlay */}
        <div className="hero-overlay" />

        {/* Geometric Shapes */}
        <Box
          sx={{
            position: 'absolute',
            top: '20%',
            right: '10%',
            zIndex: 2
          }}
        >
          <div className="geometric-shape shape-hexagon shape-hexagon-blue" />
        </Box>

        <Box
          sx={{
            position: 'absolute',
            bottom: '30%',
            left: '5%',
            zIndex: 2
          }}
        >
          <div className="geometric-shape shape-triangle shape-triangle-green" />
        </Box>

        <Container maxWidth="lg" className="hero-content">
          <Grid container spacing={8} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, x: -100, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                transition={{ duration: 1.2, ease: "easeOut" }}
              >
                <motion.div
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, delay: 0.3 }}
                >
                  {/* AgriIntel Brand */}
                  <Typography
                    variant="h1"
                    className="hero-title text-gradient"
                    sx={{
                      mb: 2,
                      fontSize: { xs: '4rem', md: '6rem', lg: '8rem' },
                      lineHeight: 0.9,
                      letterSpacing: '-0.03em'
                    }}
                  >
                    AgriIntel
                  </Typography>

                  {/* Slogan */}
                  <Typography
                    variant="h3"
                    className="hero-subtitle"
                    sx={{
                      mb: 2,
                      fontSize: { xs: '1.8rem', md: '2.5rem', lg: '3rem' },
                      fontStyle: 'italic'
                    }}
                  >
                    Smart Farming, Smarter Decisions
                  </Typography>

                  {/* Dynamic Title */}
                  <Typography
                    variant="h2"
                    className="text-gradient"
                    sx={{
                      fontWeight: 700,
                      mb: 4,
                      fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4.5rem' },
                      lineHeight: 1.1,
                      letterSpacing: '-0.02em'
                    }}
                  >
                    {translate(heroSlides[currentSlide].titleKey, {
                      fallback: 'Transform Your Livestock Management'
                    })}
                  </Typography>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, delay: 0.5 }}
                >
                  {/* Subtitle */}
                  <Typography
                    variant="h4"
                    sx={{
                      color: '#cbd5e1',
                      mb: 3,
                      fontWeight: 400,
                      fontSize: { xs: '1.5rem', md: '2rem', lg: '2.3rem' },
                      lineHeight: 1.4,
                      maxWidth: '95%'
                    }}
                  >
                    {translate(heroSlides[currentSlide].subtitleKey, {
                      fallback: 'AI-Powered Smart Farming Solutions'
                    })}
                  </Typography>

                  {/* Who We Are & What We Do */}
                  <Box className="glass-card hover-lift" sx={{
                    p: 4,
                    mb: 6,
                    borderRadius: '20px'
                  }}>
                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        color: 'white',
                        mb: 2,
                        fontSize: { xs: '1.3rem', md: '1.6rem', lg: '1.8rem' }
                      }}
                    >
                      🌍 Who We Are
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: '#e2e8f0',
                        fontSize: { xs: '1.1rem', md: '1.3rem', lg: '1.4rem' },
                        lineHeight: 1.7,
                        mb: 3
                      }}
                    >
                      South Africa's leading agricultural technology platform, empowering farmers with
                      cutting-edge livestock management solutions across the continent.
                    </Typography>

                    <Typography
                      variant="h5"
                      sx={{
                        fontWeight: 700,
                        color: 'white',
                        mb: 2,
                        fontSize: { xs: '1.3rem', md: '1.6rem', lg: '1.8rem' }
                      }}
                    >
                      🚀 What We Do
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: '#e2e8f0',
                        fontSize: { xs: '1.1rem', md: '1.3rem', lg: '1.4rem' },
                        lineHeight: 1.7
                      }}
                    >
                      We combine artificial intelligence, IoT sensors, and advanced data analytics to
                      revolutionize livestock management - from small-scale farmers to large commercial
                      operations. 🌾✨
                    </Typography>
                  </Box>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, delay: 0.7 }}
                >
                  <Box className="hero-cta-container" sx={{ mt: 6 }}>
                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={<PlayArrow />}
                        onClick={() => navigate('/beta')}
                        className="hero-cta-primary modern-button"
                        sx={{
                          fontSize: '1.3rem',
                          fontWeight: 'bold'
                        }}
                      >
                        {translate('landing.hero.tryBeta', { fallback: 'Try Beta Free' })}
                      </Button>
                    </motion.div>

                    <motion.div
                      whileHover={{ scale: 1.05, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="outlined"
                        size="large"
                        startIcon={<ArrowForward />}
                        onClick={() => setShowVideo(true)}
                        className="hero-cta-secondary"
                        sx={{
                          fontSize: '1.3rem',
                          fontWeight: 'bold'
                        }}
                      >
                        {translate('landing.hero.watchDemo', { fallback: 'Watch Demo' })}
                      </Button>
                    </motion.div>
                  </Box>
                </motion.div>
              </motion.div>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                {/* Gallery Showcase Grid */}
                <Box sx={{ position: 'relative' }}>
                  <Grid container spacing={2}>
                    {/* Main Featured Image */}
                    <Grid item xs={12}>
                      <Box
                        sx={{
                          position: 'relative',
                          borderRadius: lifeNatureDesign.shapes.borderRadius.large,
                          overflow: 'hidden',
                          background: lifeNatureDesign.gradients.primary,
                          p: 2
                        }}
                      >
                        <img
                          src="https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                          alt="Modern Livestock Management"
                          className="gallery-image-main"
                        />
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 16,
                            left: 16,
                            right: 16,
                            background: alpha(lifeNatureDesign.colors.primary, 0.9),
                            backdropFilter: 'blur(20px)',
                            borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                            p: 2
                          }}
                        >
                          <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                            🐄 Advanced Livestock Tracking
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {/* Gallery Grid */}
                    <Grid item xs={6}>
                      <Box
                        sx={{
                          borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                          overflow: 'hidden',
                          background: lifeNatureDesign.gradients.secondary,
                          p: 1.5
                        }}
                      >
                        <img
                          src="https://images.unsplash.com/photo-*************-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                          alt="Health Management"
                          className="gallery-image-small"
                        />
                        <Typography variant="caption" sx={{ color: 'white', fontWeight: 'bold', mt: 1, display: 'block' }}>
                          🩺 Health Monitoring
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={6}>
                      <Box
                        sx={{
                          borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                          overflow: 'hidden',
                          background: lifeNatureDesign.gradients.harvest,
                          p: 1.5
                        }}
                      >
                        <img
                          src="https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                          alt="Feed Management"
                          className="gallery-image-small"
                        />
                        <Typography variant="caption" sx={{ color: 'white', fontWeight: 'bold', mt: 1, display: 'block' }}>
                          🌾 Smart Feeding
                        </Typography>
                      </Box>
                    </Grid>

                    {/* RFID Technology */}
                    <Grid item xs={12}>
                      <Box
                        sx={{
                          borderRadius: lifeNatureDesign.shapes.borderRadius.medium,
                          overflow: 'hidden',
                          background: lifeNatureDesign.gradients.accent,
                          p: 1.5
                        }}
                      >
                        <img
                          src="https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                          alt="RFID Technology"
                          className="gallery-image-medium"
                        />
                        <Typography variant="body2" sx={{ color: 'white', fontWeight: 'bold', mt: 1 }}>
                          📡 Advanced RFID & IoT Integration
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>

        {/* Slide Indicators */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 30,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 1,
            zIndex: 2
          }}
        >
          {heroSlides.map((_, index) => (
            <Box
              key={index}
              onClick={() => setCurrentSlide(index)}
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                background: index === currentSlide ? 'white' : alpha('#fff', 0.5),
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Stats Section */}
      <Box className="stats-section">
        <div className="stats-background" />

        <Container maxWidth="lg" className="stats-container">
          <div className="stats-grid">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.15 }}
                viewport={{ once: true }}
                whileHover={{
                  scale: 1.1,
                  transition: { duration: 0.3 }
                }}
              >
                <div className="stat-card glass-card hover-lift">
                  {/* Icon */}
                  <motion.div
                    animate={{
                      rotate: [0, 10, -10, 0],
                      scale: [1, 1.1, 1]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Box
                      sx={{
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 2,
                        color: 'white',
                        boxShadow: '0 8px 32px rgba(59, 130, 246, 0.4)'
                      }}
                    >
                      {React.cloneElement(stat.icon, { fontSize: 'large' })}
                    </Box>
                  </motion.div>

                  {/* Number with counting animation */}
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.5 }}
                    viewport={{ once: true }}
                  >
                    <Typography
                      variant="h2"
                      className="stat-number text-gradient"
                      sx={{
                        fontSize: { xs: '2rem', md: '2.5rem' }
                      }}
                    >
                      {translate(stat.numberKey, { fallback: '10K+' })}
                    </Typography>
                  </motion.div>

                  <Typography
                    variant="h6"
                    className="stat-label"
                  >
                    {translate(stat.labelKey, { fallback: 'Metric' })}
                  </Typography>
                </div>
              </motion.div>
            ))}
          </div>
        </Container>
      </Box>

      {/* Section Divider */}
      <SectionDivider variant="wave" />

      {/* Features Section */}
      <Box className="features-section">
        <Container maxWidth="lg" className="features-container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            viewport={{ once: true }}
            className="features-header"
          >
            <Typography
              variant="h2"
              className="features-title text-gradient"
              sx={{
                fontSize: { xs: '2.8rem', md: '4rem' },
                letterSpacing: '-0.02em'
              }}
            >
              {translate('landing.features.title', {
                fallback: 'Powerful Features for Modern Farming'
              })}
            </Typography>

            <Typography
              variant="h5"
              className="features-subtitle"
              sx={{
                fontSize: { xs: '1.2rem', md: '1.4rem' }
              }}
            >
              {translate('landing.features.subtitle', {
                fallback: 'Everything you need to manage your livestock operation efficiently and profitably'
              })}
            </Typography>
          </motion.div>

          <div className="features-grid">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 60, rotateX: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 100
                }}
                viewport={{ once: true }}
                onHoverStart={() => setHoveredFeature(index)}
                onHoverEnd={() => setHoveredFeature(null)}
              >
                <div className="feature-card glass-card hover-lift hover-glow">
                  {/* Feature Icon */}
                  <motion.div
                    animate={hoveredFeature === index ? {
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, 0]
                    } : {
                      scale: [1, 1.05, 1]
                    }}
                    transition={{
                      duration: hoveredFeature === index ? 0.8 : 3,
                      repeat: hoveredFeature === index ? 1 : Infinity,
                      ease: "easeInOut"
                    }}
                    className="feature-icon"
                    style={{
                      background: feature.gradient
                    }}
                  >
                    {React.cloneElement(feature.icon, { fontSize: 'large' })}
                  </motion.div>

                  {/* Feature Content */}
                  <div>
                    <Typography
                      variant="h4"
                      className="feature-title"
                    >
                      {translate(feature.titleKey, { fallback: 'Feature Title' })}
                    </Typography>

                    <Typography
                      variant="body1"
                      className="feature-description"
                    >
                      {translate(feature.descriptionKey, { fallback: 'Feature description' })}
                    </Typography>
                  </div>

                  {/* Animated progress indicator */}
                  <motion.div
                    initial={{ width: 0, opacity: 0 }}
                    whileInView={{ width: '80%', opacity: 1 }}
                    transition={{ duration: 1.2, delay: index * 0.1 + 0.5 }}
                    viewport={{ once: true }}
                    style={{
                      height: '4px',
                      background: feature.gradient,
                      borderRadius: '2px',
                      marginTop: '20px',
                      marginLeft: 'auto',
                      marginRight: 'auto',
                      boxShadow: `0 2px 8px ${alpha(feature.color, 0.4)}`
                    }}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </Container>
      </Box>

      {/* Section Divider */}
      <SectionDivider variant="curve" />

      {/* Testimonials Section */}
      <Box sx={{ py: 10, background: `linear-gradient(135deg, ${alpha(modernDesign.colors.primary, 0.05)}, ${alpha(modernDesign.colors.accent, 0.05)})` }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                background: `linear-gradient(45deg, ${modernDesign.colors.primary}, ${modernDesign.colors.accent})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              What Our Farmers Say
            </Typography>

            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              sx={{ mb: 6, maxWidth: 600, mx: 'auto' }}
            >
              Join thousands of successful farmers who trust AgriIntel to manage their livestock operations
            </Typography>
          </motion.div>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      background: 'white',
                      borderRadius: 4,
                      boxShadow: `0 8px 32px ${alpha(modernDesign.colors.primary, 0.1)}`,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: `0 16px 48px ${alpha(modernDesign.colors.primary, 0.2)}`
                      }
                    }}
                  >
                    <CardContent sx={{ p: 4 }}>
                      {/* Rating Stars */}
                      <Box sx={{ display: 'flex', mb: 2 }}>
                        {Array.from({ length: testimonial.rating }).map((_, i) => (
                          <Star key={i} sx={{ color: '#FFD700', fontSize: 20 }} />
                        ))}
                      </Box>

                      {/* Quote */}
                      <Typography
                        variant="body1"
                        sx={{
                          fontStyle: 'italic',
                          mb: 3,
                          lineHeight: 1.6,
                          color: 'text.secondary'
                        }}
                      >
                        "{testimonial.quote}"
                      </Typography>

                      {/* Author Info */}
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          src={testimonial.image}
                          sx={{
                            width: 50,
                            height: 50,
                            mr: 2,
                            background: modernDesign.colors.gradient
                          }}
                        >
                          {testimonial.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                            {testimonial.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {testimonial.title}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {testimonial.location}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Client Logos Section */}
      <Box sx={{ py: 8, background: 'white' }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h4"
              align="center"
              sx={{
                fontWeight: 'bold',
                mb: 6,
                color: 'text.secondary'
              }}
            >
              Trusted by Leading Agricultural Organizations
            </Typography>

            <Grid container spacing={4} alignItems="center" justifyContent="center">
              {clientLogos.map((client, index) => (
                <Grid item xs={6} sm={4} md={2} key={index}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ scale: 1.1 }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: 80,
                        p: 2,
                        borderRadius: 2,
                        background: alpha('#000', 0.02),
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        '&:hover': {
                          background: alpha(modernDesign.colors.accent, 0.1),
                          transform: 'translateY(-4px)'
                        }
                      }}
                      onClick={() => window.open(client.url, '_blank')}
                    >
                      <img
                        src={client.logo}
                        alt={client.name}
                        style={{
                          maxHeight: '60px',
                          maxWidth: '120px',
                          objectFit: 'contain',
                          filter: 'grayscale(100%)',
                          transition: 'filter 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.filter = 'grayscale(0%)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.filter = 'grayscale(100%)';
                        }}
                      />
                    </Box>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Container>
      </Box>

      {/* Section Divider */}
      <SectionDivider variant="geometric" />

      {/* Subscription Plans */}
      <Box sx={{ py: 10, background: `linear-gradient(135deg, ${alpha('#000', 0.02)}, ${alpha(modernDesign.colors.primary, 0.05)})` }}>
        <Container maxWidth="lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                background: `linear-gradient(45deg, ${modernDesign.colors.primary}, ${modernDesign.colors.accent})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              Simple, Transparent Pricing
            </Typography>

            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              sx={{ mb: 2, maxWidth: 600, mx: 'auto' }}
            >
              Start free, scale as you grow. No hidden fees, cancel anytime.
            </Typography>

            <Typography
              variant="body1"
              align="center"
              sx={{
                mb: 6,
                maxWidth: 500,
                mx: 'auto',
                color: modernDesign.colors.accent,
                fontWeight: 'bold'
              }}
            >
              🎉 Limited Time: 25% off Professional & Enterprise plans!
            </Typography>
          </motion.div>

          <Grid container spacing={4} justifyContent="center">
            {subscriptionTiers.map((tier, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      position: 'relative',
                      background: tier.popular
                        ? `linear-gradient(135deg, ${alpha(tier.color, 0.1)}, ${alpha(tier.color, 0.05)})`
                        : 'white',
                      border: tier.popular
                        ? `3px solid ${tier.color}`
                        : `1px solid ${alpha('#000', 0.1)}`,
                      borderRadius: 4,
                      transform: tier.popular ? 'scale(1.05)' : 'scale(1)',
                      transition: 'all 0.3s ease',
                      overflow: 'visible',
                      '&:hover': {
                        transform: tier.popular ? 'scale(1.08)' : 'scale(1.03)',
                        boxShadow: `0 20px 60px ${alpha(tier.color, 0.25)}`
                      }
                    }}
                  >
                    {tier.popular && (
                      <Chip
                        label={tier.badge}
                        sx={{
                          position: 'absolute',
                          top: -12,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          background: `linear-gradient(45deg, ${tier.color}, ${tier.color}80)`,
                          color: 'white',
                          fontWeight: 'bold',
                          fontSize: '0.875rem',
                          px: 2
                        }}
                      />
                    )}

                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                      {/* Plan Header */}
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                          {React.cloneElement(tier.icon, {
                            sx: { fontSize: 48, color: tier.color, mr: 1 }
                          })}
                        </Box>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {tier.nameKey.includes('beta') ? 'Beta Access' :
                           tier.nameKey.includes('professional') ? 'Professional' : 'Enterprise'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {tier.animalLimit}
                        </Typography>
                      </Box>

                      {/* Pricing */}
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'center', mb: 1 }}>
                          <Typography
                            variant="h2"
                            sx={{
                              fontWeight: 'bold',
                              color: tier.color,
                              lineHeight: 1
                            }}
                          >
                            {tier.price}
                          </Typography>
                          {tier.price !== 'Free' && (
                            <Typography variant="body1" color="text.secondary" sx={{ ml: 1 }}>
                              /month
                            </Typography>
                          )}
                        </Box>

                        {tier.originalPrice && (
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                textDecoration: 'line-through',
                                color: 'text.disabled'
                              }}
                            >
                              {tier.originalPrice}
                            </Typography>
                            <Chip
                              label={`Save ${tier.savings}`}
                              size="small"
                              sx={{
                                background: alpha('#4CAF50', 0.1),
                                color: '#4CAF50',
                                fontWeight: 'bold'
                              }}
                            />
                          </Box>
                        )}
                      </Box>

                      {/* Features */}
                      <Box sx={{ mb: 4, textAlign: 'left' }}>
                        {tier.features.map((feature, featureIndex) => (
                          <Box
                            key={featureIndex}
                            sx={{
                              display: 'flex',
                              alignItems: 'flex-start',
                              mb: 1.5,
                              gap: 1
                            }}
                          >
                            <CheckCircle
                              sx={{
                                color: tier.color,
                                fontSize: 18,
                                mt: 0.25,
                                flexShrink: 0
                              }}
                            />
                            <Typography variant="body2" sx={{ lineHeight: 1.5 }}>
                              {feature}
                            </Typography>
                          </Box>
                        ))}
                      </Box>

                      <Button
                        variant={tier.popular ? "contained" : "outlined"}
                        fullWidth
                        size="large"
                        onClick={() => {
                          if (tier.nameKey.includes('beta')) {
                            navigate('/beta');
                          } else {
                            navigate('/register', { state: { selectedPlan: tier.nameKey.includes('professional') ? 'Professional' : 'Enterprise' } });
                          }
                        }}
                        sx={{
                          background: tier.popular
                            ? tier.gradient
                            : 'transparent',
                          borderColor: tier.color,
                          color: tier.popular ? 'white' : tier.color,
                          py: 2,
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          borderRadius: 3,
                          textTransform: 'none',
                          boxShadow: tier.popular ? `0 8px 24px ${alpha(tier.color, 0.3)}` : 'none',
                          '&:hover': {
                            background: tier.popular
                              ? `linear-gradient(45deg, ${tier.color}90, ${tier.color}70)`
                              : alpha(tier.color, 0.1),
                            transform: 'translateY(-2px)',
                            boxShadow: `0 12px 32px ${alpha(tier.color, 0.4)}`
                          }
                        }}
                      >
                        {tier.price === 'Free' ? 'Start Free Trial' :
                         tier.popular ? 'Get Started Now' : 'Contact Sales'}
                      </Button>

                      {/* Additional CTA for non-free plans */}
                      {tier.price !== 'Free' && (
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ mt: 2, display: 'block' }}
                        >
                          14-day free trial • No credit card required
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Final Call-to-Action Section */}
      <Box
        sx={{
          py: 10,
          background: `linear-gradient(135deg, ${modernDesign.colors.primary}, ${modernDesign.colors.accent})`,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.3
          }}
        />

        <Container maxWidth="md" sx={{ position: 'relative', zIndex: 1 }}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            style={{ textAlign: 'center' }}
          >
            <Typography
              variant="h2"
              sx={{
                fontWeight: 'bold',
                color: 'white',
                mb: 3,
                fontSize: { xs: '2.5rem', md: '3.5rem' }
              }}
            >
              Ready to Transform Your Farm?
            </Typography>

            <Typography
              variant="h5"
              sx={{
                color: alpha('#fff', 0.9),
                mb: 4,
                lineHeight: 1.6,
                maxWidth: 600,
                mx: 'auto'
              }}
            >
              Join thousands of successful farmers who've increased their profitability by 30% with AgriIntel's smart livestock management platform.
            </Typography>

            <Box sx={{ display: 'flex', gap: 3, justifyContent: 'center', flexWrap: 'wrap' }}>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={() => navigate('/beta')}
                  sx={{
                    background: 'white',
                    color: modernDesign.colors.primary,
                    px: 4,
                    py: 2,
                    fontSize: '1.2rem',
                    fontWeight: 'bold',
                    borderRadius: 3,
                    textTransform: 'none',
                    boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
                    '&:hover': {
                      background: alpha('#fff', 0.95),
                      transform: 'translateY(-2px)',
                      boxShadow: '0 12px 40px rgba(0,0,0,0.3)'
                    }
                  }}
                >
                  Start Free Beta
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outlined"
                  size="large"
                  onClick={() => setShowVideo(true)}
                  sx={{
                    borderColor: 'white',
                    color: 'white',
                    px: 4,
                    py: 2,
                    fontSize: '1.2rem',
                    fontWeight: 'bold',
                    borderRadius: 3,
                    textTransform: 'none',
                    borderWidth: 2,
                    '&:hover': {
                      borderColor: 'white',
                      background: alpha('#fff', 0.1),
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  Watch Demo
                </Button>
              </motion.div>
            </Box>

            <Typography
              variant="body2"
              sx={{
                color: alpha('#fff', 0.8),
                mt: 3
              }}
            >
              No credit card required • 14-day free trial • Cancel anytime
            </Typography>
          </motion.div>
        </Container>
      </Box>

      {/* Video Demo Dialog */}
      <Dialog
        open={showVideo}
        onClose={() => setShowVideo(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden'
          }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <Box
            sx={{
              position: 'relative',
              paddingBottom: '56.25%', // 16:9 aspect ratio
              height: 0,
              overflow: 'hidden'
            }}
          >
            <iframe
              src="https://www.youtube.com/embed/dQw4w9WgXcQ"
              title="AgriIntel Demo"
              className="demo-video-iframe"
              allowFullScreen
            />
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default LandingPage;
